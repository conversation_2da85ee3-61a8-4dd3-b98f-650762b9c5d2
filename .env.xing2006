# MySQL 数据库配置 - 独立配置避免冲突
MYSQL_ROOT_PASSWORD=ghost_root_password_xing2006_2024
MYSQL_DATABASE=ghost_production_xing2006
MYSQL_USER=ghost_user_xing2006
MYSQL_PASSWORD=ghost_password_xing2006_2024

# Ghost 应用配置
GHOST_URL=https://blog.xing2006.me
GHOST_LOCALE=zh
GHOST_ADMIN_EMAIL=<EMAIL>

# 邮件配置（可选）
MAIL_FROM=<EMAIL>
MAIL_HOST=smtp.xing2006.me
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password

# SSL 配置
SSL_EMAIL=<EMAIL>
DOMAIN_NAME=blog.xing2006.me

# 数据库连接配置
DB_HOST=mysql
DB_PORT=3306
DB_NAME=ghost_production_xing2006
DB_USER=ghost_user_xing2006
DB_PASSWORD=ghost_password_xing2006_2024

# Ghost 运行配置
NODE_ENV=production
GHOST_INSTALL=/var/lib/ghost
GHOST_CONTENT=/var/lib/ghost/content

# 多站点环境快速部署指南

## 🎯 部署目标

在您的 `xing2006.me` 域名下添加 Ghost 博客，不影响现有的两个应用：
- 现有应用继续正常运行
- 新增 `blog.xing2006.me` 博客站点
- 使用独立的端口和网络避免冲突

## 🚀 一键部署（推荐）

### 前置要求
- VPS 已有 Docker 和 Nginx
- 域名 `xing2006.me` 正常解析
- SSH 访问权限

### 步骤 1：配置 DNS 解析
在域名管理面板添加 A 记录：
```
类型: A
名称: blog
值: your-vps-ip
TTL: 300
```

### 步骤 2：运行自动部署
```bash
# 给脚本执行权限
chmod +x scripts/deploy-multi-site.sh

# 执行部署
./scripts/deploy-multi-site.sh xing2006.me your-vps-ip
```

### 步骤 3：访问博客
- 博客首页: https://blog.xing2006.me
- 管理后台: https://blog.xing2006.me/ghost/

## 🔧 手动部署步骤

如果自动部署失败，可以手动执行：

### 1. 连接到 VPS
```bash
ssh root@your-vps-ip
```

### 2. 创建项目目录
```bash
mkdir -p /opt/ghost-blog-xing2006
cd /opt/ghost-blog-xing2006
```

### 3. 上传项目文件
```bash
# 在本地执行
scp -r ./* root@your-vps-ip:/opt/ghost-blog-xing2006/
```

### 4. 配置环境变量
```bash
# 在 VPS 上执行
cd /opt/ghost-blog-xing2006
cp .env.xing2006 .env.production

# 检查配置
cat .env.production
```

### 5. 创建数据目录
```bash
mkdir -p data/{ghost-content,mysql-data}
mkdir -p logs/nginx
chown -R 1000:1000 data/ghost-content
```

### 6. 配置 Nginx 虚拟主机
```bash
# 创建虚拟主机配置
cat > /etc/nginx/sites-available/blog.xing2006.me << 'EOF'
server {
    listen 80;
    server_name blog.xing2006.me;
    
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }
    
    location / {
        return 301 https://$server_name$request_uri;
    }
}

server {
    listen 443 ssl http2;
    server_name blog.xing2006.me;

    ssl_certificate /etc/letsencrypt/live/blog.xing2006.me/fullchain.pem;
    ssl_private_key /etc/letsencrypt/live/blog.xing2006.me/privkey.pem;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_session_cache shared:SSL:10m;

    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    location / {
        proxy_pass http://127.0.0.1:3368;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
EOF

# 启用虚拟主机
ln -s /etc/nginx/sites-available/blog.xing2006.me /etc/nginx/sites-enabled/
```

### 7. 获取 SSL 证书
```bash
# 安装 certbot（如果未安装）
apt install certbot -y

# 创建验证目录
mkdir -p /var/www/certbot

# 获取证书
certbot certonly --webroot -w /var/www/certbot -d blog.xing2006.me --email <EMAIL> --agree-tos --non-interactive
```

### 8. 启动 Ghost 服务
```bash
# 启动服务
docker-compose -f docker-compose.multi-site.yml --env-file .env.production up -d

# 检查状态
docker-compose -f docker-compose.multi-site.yml ps
```

### 9. 重载 Nginx
```bash
# 测试配置
nginx -t

# 重载配置
nginx -s reload
```

## 🔍 验证部署

### 检查服务状态
```bash
# 检查 Ghost 容器
docker ps | grep ghost

# 检查端口
netstat -tlnp | grep -E ':(3368|3307)'

# 测试直接访问
curl -I http://localhost:3368
```

### 检查现有应用
确保现有应用仍然正常：
```bash
# 测试主站点
curl -I https://xing2006.me

# 检查其他应用
docker ps
```

### 访问测试
- 主站点：https://xing2006.me ✅
- Open WebUI：应该正常 ✅
- Gemini Balance：应该正常 ✅
- Ghost 博客：https://blog.xing2006.me ✅

## 🛠️ 管理命令

### Ghost 博客管理
```bash
cd /opt/ghost-blog-xing2006

# 查看状态
docker-compose -f docker-compose.multi-site.yml ps

# 查看日志
docker-compose -f docker-compose.multi-site.yml logs -f

# 重启服务
docker-compose -f docker-compose.multi-site.yml restart

# 停止服务
docker-compose -f docker-compose.multi-site.yml down

# 更新服务
docker-compose -f docker-compose.multi-site.yml pull
docker-compose -f docker-compose.multi-site.yml up -d
```

### 备份数据
```bash
# 备份数据库
docker-compose -f docker-compose.multi-site.yml exec mysql mysqldump -u ghost_user_xing2006 -p ghost_production_xing2006 > backup-$(date +%Y%m%d).sql

# 备份内容
tar -czf ghost-content-backup-$(date +%Y%m%d).tar.gz data/ghost-content/
```

## 🔧 故障排除

### 端口冲突
```bash
# 检查端口占用
ss -tlnp | grep -E ':(3368|3307)'

# 如果冲突，修改 docker-compose.multi-site.yml 中的端口
```

### 域名无法访问
```bash
# 检查 DNS 解析
nslookup blog.xing2006.me

# 检查 Nginx 配置
nginx -t

# 查看 Nginx 错误日志
tail -f /var/log/nginx/error.log
```

### SSL 证书问题
```bash
# 检查证书
certbot certificates

# 手动续期
certbot renew --dry-run
```

### Ghost 无法启动
```bash
# 查看 Ghost 日志
docker-compose -f docker-compose.multi-site.yml logs ghost

# 检查数据库连接
docker-compose -f docker-compose.multi-site.yml exec mysql mysql -u ghost_user_xing2006 -p
```

## 📋 部署架构

```
Internet
    ↓
Nginx (80/443)
    ├── xing2006.me → 现有应用
    ├── Open WebUI → 现有应用
    ├── Gemini Balance → 现有应用
    └── blog.xing2006.me → Ghost (端口 3368)
                              ↓
                           MySQL (端口 3307)
```

## 🎯 关键特性

- ✅ **独立端口**：Ghost 使用 3368，MySQL 使用 3307
- ✅ **独立网络**：使用 **********/16 子网
- ✅ **独立数据**：完全隔离的数据存储
- ✅ **独立管理**：可以单独更新和维护
- ✅ **不影响现有应用**：现有服务继续正常运行

## 🎉 部署成功

恭喜！您现在拥有：
1. 原有的 xing2006.me 主站点
2. 原有的 Open WebUI 应用
3. 原有的 Gemini Balance 应用
4. 新的 blog.xing2006.me Ghost 博客

所有应用都可以独立管理和维护！

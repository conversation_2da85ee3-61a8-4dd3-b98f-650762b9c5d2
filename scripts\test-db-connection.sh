#!/bin/bash

# 数据库连接测试脚本
# 用于验证 MySQL 数据库连接是否正常

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 加载环境变量
if [ -f .env ]; then
    source .env
    log_info "环境变量已加载"
else
    log_error ".env 文件不存在"
    exit 1
fi

# 检查必要的环境变量
if [ -z "$MYSQL_USER" ] || [ -z "$MYSQL_PASSWORD" ] || [ -z "$MYSQL_DATABASE" ]; then
    log_error "缺少必要的数据库环境变量"
    exit 1
fi

log_info "开始测试数据库连接..."

# 等待 MySQL 容器启动
log_info "等待 MySQL 容器启动..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if docker-compose exec -T mysql mysqladmin ping -h localhost --silent; then
        log_info "MySQL 容器已启动"
        break
    else
        log_warn "等待 MySQL 容器启动... (尝试 $attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    fi
done

if [ $attempt -gt $max_attempts ]; then
    log_error "MySQL 容器启动超时"
    exit 1
fi

# 测试数据库连接
log_info "测试数据库连接..."
if docker-compose exec -T mysql mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -h localhost "$MYSQL_DATABASE" -e "SELECT 1;" > /dev/null 2>&1; then
    log_info "数据库连接成功"
else
    log_error "数据库连接失败"
    exit 1
fi

# 测试数据库表
log_info "检查测试表..."
if docker-compose exec -T mysql mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -h localhost "$MYSQL_DATABASE" -e "SELECT * FROM connection_test LIMIT 1;" > /dev/null 2>&1; then
    log_info "测试表存在且可访问"
    
    # 显示测试表内容
    log_info "测试表内容："
    docker-compose exec -T mysql mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -h localhost "$MYSQL_DATABASE" -e "SELECT * FROM connection_test;"
else
    log_warn "测试表不存在或无法访问"
fi

# 显示数据库信息
log_info "数据库信息："
docker-compose exec -T mysql mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -h localhost "$MYSQL_DATABASE" -e "
    SELECT 
        DATABASE() as current_database,
        USER() as current_user,
        VERSION() as mysql_version,
        @@character_set_database as charset,
        @@collation_database as collation;
"

log_info "数据库连接测试完成"

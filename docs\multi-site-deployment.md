# 多站点环境下的 Ghost 博客部署指南

## 🏗️ 部署架构

### 当前环境分析
- 主域名: xing2006.me
- 现有应用: Open WebUI, Gemini Balance
- 新增应用: Ghost 博客

### 部署策略
```
xing2006.me (主站点)
├── Open WebUI (现有)
├── Gemini Balance (现有)  
└── blog.xing2006.me (新增 Ghost 博客)
```

## 🚀 部署方案

### 方案一：独立端口 + Nginx 反向代理（推荐）

#### 1.1 架构设计
```
Internet → Nginx (80/443) → 根据域名路由
├── xing2006.me → 现有应用
├── Open WebUI → 现有应用  
├── Gemini Balance → 现有应用
└── blog.xing2006.me → Ghost (端口 3368)
```

#### 1.2 优势
- 不影响现有应用
- 独立的容器网络
- 易于管理和维护
- 可以独立更新

## 📁 第一步：创建独立项目目录

```bash
# 在 VPS 上创建独立目录
mkdir -p /opt/ghost-blog-xing2006
cd /opt/ghost-blog-xing2006
```

## ⚙️ 第二步：修改配置文件

### 2.1 创建独立的 Docker Compose 配置
```yaml
# docker-compose.multi-site.yml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: ghost_mysql_xing2006
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data_xing2006:/var/lib/mysql
    networks:
      - ghost_network_xing2006
    ports:
      - "3307:3306"  # 使用不同端口避免冲突
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  ghost:
    image: ghost:5-alpine
    container_name: ghost_app_xing2006
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      database__client: mysql
      database__connection__host: mysql
      database__connection__user: ${MYSQL_USER}
      database__connection__password: ${MYSQL_PASSWORD}
      database__connection__database: ${MYSQL_DATABASE}
      url: ${GHOST_URL}
    volumes:
      - ghost_content_xing2006:/var/lib/ghost/content
      - ./ghost/config.production.json:/var/lib/ghost/config.production.json:ro
    networks:
      - ghost_network_xing2006
    ports:
      - "3368:2368"  # 使用独立端口
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:2368/"]
      timeout: 30s
      retries: 3

volumes:
  ghost_content_xing2006:
  mysql_data_xing2006:

networks:
  ghost_network_xing2006:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16  # 使用不同的子网
```

### 2.2 环境变量配置
```env
# .env.xing2006
MYSQL_ROOT_PASSWORD=your_secure_root_password_xing2006
MYSQL_DATABASE=ghost_production_xing2006
MYSQL_USER=ghost_user_xing2006
MYSQL_PASSWORD=your_secure_ghost_password_xing2006

GHOST_URL=https://blog.xing2006.me
GHOST_LOCALE=zh
GHOST_ADMIN_EMAIL=<EMAIL>

SSL_EMAIL=<EMAIL>
DOMAIN_NAME=blog.xing2006.me
```

## 🌐 第三步：配置 Nginx 虚拟主机

### 3.1 检查现有 Nginx 配置
```bash
# 查看现有 Nginx 配置
nginx -t
ls -la /etc/nginx/sites-available/
ls -la /etc/nginx/conf.d/
```

### 3.2 添加 Ghost 博客虚拟主机
创建 `/etc/nginx/sites-available/blog.xing2006.me`：

```nginx
# Ghost 博客虚拟主机配置
server {
    listen 80;
    server_name blog.xing2006.me;
    
    # Let's Encrypt 验证路径
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }
    
    # 重定向到 HTTPS
    location / {
        return 301 https://$server_name$request_uri;
    }
}

server {
    listen 443 ssl http2;
    server_name blog.xing2006.me;

    # SSL 证书配置
    ssl_certificate /etc/letsencrypt/live/blog.xing2006.me/fullchain.pem;
    ssl_private_key /etc/letsencrypt/live/blog.xing2006.me/privkey.pem;
    
    # SSL 安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;

    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;

    # 反向代理到 Ghost
    location / {
        proxy_pass http://127.0.0.1:3368;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        proxy_pass http://127.0.0.1:3368;
        proxy_set_header Host $host;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 3.3 启用虚拟主机
```bash
# 创建软链接
ln -s /etc/nginx/sites-available/blog.xing2006.me /etc/nginx/sites-enabled/

# 测试配置
nginx -t

# 重载 Nginx（不会影响现有站点）
nginx -s reload
```

## 🔒 第四步：配置 SSL 证书

### 4.1 为新域名获取 SSL 证书
```bash
# 为 blog.xing2006.me 获取独立证书
certbot certonly --webroot -w /var/www/certbot -d blog.xing2006.me --email <EMAIL> --agree-tos --non-interactive

# 或者使用 DNS 验证（推荐）
certbot certonly --manual --preferred-challenges dns -d blog.xing2006.me
```

### 4.2 配置自动续期
```bash
# 检查现有的续期配置
crontab -l

# 如果没有，添加续期任务
echo "0 12 * * * /usr/bin/certbot renew --quiet && nginx -s reload" | crontab -
```

## 🚀 第五步：部署 Ghost 博客

### 5.1 上传项目文件
```bash
# 上传到独立目录
scp -r ./* root@your-vps-ip:/opt/ghost-blog-xing2006/
```

### 5.2 启动服务
```bash
cd /opt/ghost-blog-xing2006

# 使用独立配置启动
docker-compose -f docker-compose.multi-site.yml --env-file .env.xing2006 up -d

# 检查服务状态
docker-compose -f docker-compose.multi-site.yml ps
```

## 🔍 第六步：验证部署

### 6.1 检查端口占用
```bash
# 确认端口不冲突
netstat -tlnp | grep -E ':(80|443|3368|3307)'
```

### 6.2 测试访问
```bash
# 测试 Ghost 直接访问
curl -I http://localhost:3368

# 测试通过域名访问
curl -I https://blog.xing2006.me
```

### 6.3 验证现有站点
确保现有站点仍然正常工作：
- https://xing2006.me
- Open WebUI 应用
- Gemini Balance 应用

## 🛠️ 方案二：使用子路径部署

如果不想使用二级域名，可以使用子路径：

### 2.1 Nginx 配置
```nginx
# 在现有的 xing2006.me 虚拟主机中添加
location /blog/ {
    proxy_pass http://127.0.0.1:3368/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # 重写路径
    rewrite ^/blog/(.*)$ /$1 break;
}
```

### 2.2 Ghost 配置
```json
{
  "url": "https://xing2006.me/blog",
  "server": {
    "port": 2368,
    "host": "0.0.0.0"
  }
}
```

## 📋 管理命令

### 查看所有服务状态
```bash
# 查看 Ghost 博客
cd /opt/ghost-blog-xing2006
docker-compose -f docker-compose.multi-site.yml ps

# 查看现有应用
docker ps | grep -v ghost
```

### 独立管理 Ghost 博客
```bash
# 重启 Ghost 博客
docker-compose -f docker-compose.multi-site.yml restart

# 查看日志
docker-compose -f docker-compose.multi-site.yml logs -f

# 更新 Ghost 博客
docker-compose -f docker-compose.multi-site.yml pull
docker-compose -f docker-compose.multi-site.yml up -d
```

## 🔧 故障排除

### 端口冲突
```bash
# 检查端口占用
ss -tlnp | grep -E ':(3368|3307)'

# 如果端口被占用，修改 docker-compose.multi-site.yml 中的端口映射
```

### Nginx 配置冲突
```bash
# 测试 Nginx 配置
nginx -t

# 查看错误日志
tail -f /var/log/nginx/error.log
```

### SSL 证书问题
```bash
# 检查证书状态
certbot certificates

# 手动续期
certbot renew --dry-run
```

## 🎉 部署完成

现在您可以：
1. 继续使用现有的应用：https://xing2006.me
2. 访问新的 Ghost 博客：https://blog.xing2006.me
3. 独立管理每个应用

这种部署方式确保了：
- ✅ 现有应用不受影响
- ✅ 独立的数据和配置
- ✅ 可以独立更新和维护
- ✅ 资源隔离和安全性

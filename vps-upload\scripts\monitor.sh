#!/bin/bash

# Ghost 博客监控脚本
# 用于检查服务状态和系统资源

set -e

PROJECT_DIR="/opt/ghost-blog-xing2006"

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查容器状态
check_containers() {
    echo "=== 容器状态检查 ==="
    
    cd "$PROJECT_DIR"
    
    if docker-compose -f docker-compose.multi-site.yml ps | grep -q "Up"; then
        log_info "容器状态:"
        docker-compose -f docker-compose.multi-site.yml ps
    else
        log_error "容器未运行或异常"
        docker-compose -f docker-compose.multi-site.yml ps
    fi
    
    echo
}

# 检查端口监听
check_ports() {
    echo "=== 端口监听检查 ==="
    
    PORTS=("3368" "3307" "80" "443")
    
    for port in "${PORTS[@]}"; do
        if netstat -tlnp | grep -q ":$port "; then
            log_info "端口 $port: 正在监听"
        else
            log_warn "端口 $port: 未监听"
        fi
    done
    
    echo
}

# 检查服务可访问性
check_accessibility() {
    echo "=== 服务可访问性检查 ==="
    
    # 检查 Ghost 直接访问
    if curl -f -s http://localhost:3368 > /dev/null; then
        log_info "Ghost 直接访问: 正常"
    else
        log_error "Ghost 直接访问: 失败"
    fi
    
    # 检查域名访问
    if curl -f -s https://blog.xing2006.me > /dev/null; then
        log_info "域名访问: 正常"
    else
        log_warn "域名访问: 失败"
    fi
    
    # 检查管理后台
    if curl -f -s https://blog.xing2006.me/ghost/ > /dev/null; then
        log_info "管理后台: 正常"
    else
        log_warn "管理后台: 失败"
    fi
    
    echo
}

# 检查资源使用
check_resources() {
    echo "=== 系统资源检查 ==="
    
    # 内存使用
    echo "内存使用:"
    free -h
    echo
    
    # 磁盘使用
    echo "磁盘使用:"
    df -h | grep -E "(/$|/opt)"
    echo
    
    # CPU 负载
    echo "CPU 负载:"
    uptime
    echo
    
    # Docker 资源使用
    echo "Docker 容器资源使用:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
    echo
}

# 检查日志错误
check_logs() {
    echo "=== 日志错误检查 ==="
    
    cd "$PROJECT_DIR"
    
    # 检查 Ghost 日志
    echo "Ghost 最近错误:"
    docker-compose -f docker-compose.multi-site.yml logs --tail=50 ghost | grep -i error || echo "无错误"
    echo
    
    # 检查 MySQL 日志
    echo "MySQL 最近错误:"
    docker-compose -f docker-compose.multi-site.yml logs --tail=50 mysql | grep -i error || echo "无错误"
    echo
    
    # 检查 Nginx 错误日志
    echo "Nginx 错误日志:"
    tail -n 10 /var/log/nginx/error.log 2>/dev/null || echo "无法访问 Nginx 错误日志"
    echo
}

# 检查 SSL 证书
check_ssl() {
    echo "=== SSL 证书检查 ==="
    
    if command -v certbot &> /dev/null; then
        echo "SSL 证书状态:"
        certbot certificates 2>/dev/null | grep -A 5 "blog.xing2006.me" || echo "未找到证书"
    else
        log_warn "Certbot 未安装"
    fi
    
    echo
}

# 检查数据库连接
check_database() {
    echo "=== 数据库连接检查 ==="
    
    cd "$PROJECT_DIR"
    
    if docker-compose -f docker-compose.multi-site.yml exec -T mysql mysql -u ghost_user_xing2006 -p'ghost_password_xing2006_2024' -e "SELECT 1;" > /dev/null 2>&1; then
        log_info "数据库连接: 正常"
        
        # 检查数据库大小
        DB_SIZE=$(docker-compose -f docker-compose.multi-site.yml exec -T mysql mysql -u ghost_user_xing2006 -p'ghost_password_xing2006_2024' -e "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'DB Size in MB' FROM information_schema.tables WHERE table_schema='ghost_production_xing2006';" 2>/dev/null | tail -n 1)
        echo "数据库大小: ${DB_SIZE} MB"
    else
        log_error "数据库连接: 失败"
    fi
    
    echo
}

# 生成监控报告
generate_report() {
    echo "=== 监控报告 ==="
    echo "监控时间: $(date)"
    echo "项目目录: $PROJECT_DIR"
    echo "博客地址: https://blog.xing2006.me"
    echo
    
    # 统计信息
    GHOST_STATUS=$(docker-compose -f docker-compose.multi-site.yml ps ghost | grep -q "Up" && echo "运行中" || echo "已停止")
    MYSQL_STATUS=$(docker-compose -f docker-compose.multi-site.yml ps mysql | grep -q "Up" && echo "运行中" || echo "已停止")
    
    echo "服务状态:"
    echo "- Ghost: $GHOST_STATUS"
    echo "- MySQL: $MYSQL_STATUS"
    echo
}

# 主函数
main() {
    log_info "=== Ghost 博客监控开始 ==="
    echo "监控时间: $(date)"
    echo
    
    generate_report
    check_containers
    check_ports
    check_accessibility
    check_resources
    check_database
    check_ssl
    check_logs
    
    log_info "=== 监控完成 ==="
}

# 执行监控
main "$@"

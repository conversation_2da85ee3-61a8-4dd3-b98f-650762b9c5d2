# Ghost 博客 VPS 自动部署脚本 (PowerShell 版本)
# 使用方法: .\deploy-to-vps.ps1 -Domain "yourdomain.com" -VpsIp "*******" -Subdomain "blog"

param(
    [Parameter(Mandatory=$true)]
    [string]$Domain,
    
    [Parameter(Mandatory=$true)]
    [string]$VpsIp,
    
    [string]$Subdomain = "blog",
    
    [string]$SshUser = "root",
    
    [switch]$SkipDocker,
    
    [switch]$SkipSSL,
    
    [switch]$Verbose
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 全局变量
$FullDomain = "$Subdomain.$Domain"
$ProjectDir = "/opt/ghost-blog"

# 日志函数
function Write-Info {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warn {
    param($Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Debug {
    param($Message)
    if ($Verbose) {
        Write-Host "[DEBUG] $Message" -ForegroundColor Blue
    }
}

# 检查本地环境
function Test-LocalRequirements {
    Write-Info "检查本地环境..."
    
    # 检查 SSH 客户端
    if (-not (Get-Command ssh -ErrorAction SilentlyContinue)) {
        Write-Error "SSH 客户端未安装，请安装 OpenSSH 客户端"
        exit 1
    }
    
    # 检查 SCP
    if (-not (Get-Command scp -ErrorAction SilentlyContinue)) {
        Write-Error "SCP 未安装，请安装 OpenSSH 客户端"
        exit 1
    }
    
    Write-Info "本地环境检查通过"
}

# 检查 VPS 连接
function Test-VpsConnection {
    Write-Info "检查 VPS 连接..."
    
    try {
        $result = ssh -o ConnectTimeout=10 "$SshUser@$VpsIp" "echo 'VPS 连接成功'"
        if ($LASTEXITCODE -eq 0) {
            Write-Info "VPS 连接检查通过"
        } else {
            throw "SSH 连接失败"
        }
    } catch {
        Write-Error "无法连接到 VPS: $VpsIp"
        Write-Info "请确保:"
        Write-Info "1. VPS IP 地址正确"
        Write-Info "2. SSH 密钥已配置"
        Write-Info "3. 防火墙允许 SSH 连接"
        exit 1
    }
}

# 在 VPS 上安装 Docker
function Install-DockerOnVps {
    if ($SkipDocker) {
        Write-Info "跳过 Docker 安装"
        return
    }
    
    Write-Info "在 VPS 上安装 Docker..."
    
    $dockerInstallScript = @"
# 更新系统
apt update && apt upgrade -y

# 安装 Docker
if ! command -v docker &> /dev/null; then
    curl -fsSL https://get.docker.com -o get-docker.sh
    sh get-docker.sh
    rm get-docker.sh
fi

# 安装 Docker Compose
if ! command -v docker-compose &> /dev/null; then
    curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-`$(uname -s)-`$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
fi

# 启动 Docker 服务
systemctl enable docker
systemctl start docker

echo "Docker 安装完成"
docker --version
docker-compose --version
"@
    
    ssh "$SshUser@$VpsIp" $dockerInstallScript
    Write-Info "Docker 安装完成"
}

# 创建生产环境配置
function New-ProductionConfig {
    Write-Info "创建生产环境配置..."
    
    # 生成随机密码
    $MysqlRootPassword = [System.Web.Security.Membership]::GeneratePassword(32, 8)
    $MysqlPassword = [System.Web.Security.Membership]::GeneratePassword(32, 8)
    
    # 创建生产环境变量文件
    $envContent = @"
# MySQL 数据库配置
MYSQL_ROOT_PASSWORD=$MysqlRootPassword
MYSQL_DATABASE=ghost_production
MYSQL_USER=ghost_user
MYSQL_PASSWORD=$MysqlPassword

# Ghost 应用配置
GHOST_URL=https://$FullDomain
GHOST_LOCALE=zh
GHOST_ADMIN_EMAIL=admin@$Domain

# SSL 配置
SSL_EMAIL=admin@$Domain
DOMAIN_NAME=$FullDomain
"@
    
    $envContent | Out-File -FilePath ".env.production" -Encoding UTF8
    
    # 更新 Ghost 配置文件
    $ghostConfig = @{
        url = "https://$FullDomain"
        server = @{
            port = 2368
            host = "0.0.0.0"
        }
        i18n = @{
            defaultLocale = "zh"
        }
        database = @{
            client = "mysql"
            connection = @{
                host = "mysql"
                port = 3306
                user = "ghost_user"
                password = $MysqlPassword
                database = "ghost_production"
                charset = "utf8mb4"
            }
            pool = @{
                min = 2
                max = 10
            }
        }
        mail = @{
            transport = "Direct"
            options = @{}
        }
        logging = @{
            level = "info"
            rotation = @{
                enabled = $true
                count = 15
                period = "1d"
            }
            transports = @("file", "stdout")
        }
        process = "systemd"
        paths = @{
            contentPath = "/var/lib/ghost/content"
        }
        privacy = @{
            useUpdateCheck = $false
            useGravatar = $false
            useRpcPing = $false
            useStructuredData = $true
        }
        imageOptimization = @{
            resize = $true
            srcsets = $true
        }
        compress = $true
        caching = @{
            frontend = @{
                maxAge = 600
            }
            "301" = @{
                maxAge = 31536000
            }
        }
    }
    
    $ghostConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath "ghost/config.production.json" -Encoding UTF8
    
    Write-Info "生产环境配置创建完成"
}

# 创建 Nginx SSL 配置
function New-NginxSslConfig {
    Write-Info "创建 Nginx SSL 配置..."
    
    if (-not (Test-Path "nginx/conf.d")) {
        New-Item -ItemType Directory -Path "nginx/conf.d" -Force
    }
    
    $nginxConfig = @"
server {
    listen 80;
    server_name $FullDomain;
    
    # Let's Encrypt 验证
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }
    
    # 重定向到 HTTPS
    location / {
        return 301 https://`$server_name`$request_uri;
    }
}

server {
    listen 443 ssl http2;
    server_name $FullDomain;

    # SSL 证书配置
    ssl_certificate /etc/letsencrypt/live/$FullDomain/fullchain.pem;
    ssl_private_key /etc/letsencrypt/live/$FullDomain/privkey.pem;
    
    # SSL 安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;

    # 反向代理到 Ghost
    location / {
        proxy_pass http://ghost:2368;
        proxy_set_header Host `$host;
        proxy_set_header X-Real-IP `$remote_addr;
        proxy_set_header X-Forwarded-For `$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto `$scheme;
        proxy_set_header X-Forwarded-Host `$host;
        proxy_set_header X-Forwarded-Port `$server_port;
        
        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade `$http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 缓存配置
        proxy_cache_bypass `$http_upgrade;
        proxy_buffering off;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)`$ {
        proxy_pass http://ghost:2368;
        proxy_set_header Host `$host;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
"@
    
    $nginxConfig | Out-File -FilePath "nginx/conf.d/ssl.conf" -Encoding UTF8
    Write-Info "Nginx SSL 配置创建完成"
}

# 上传文件到 VPS
function Copy-FilesToVps {
    Write-Info "上传文件到 VPS..."
    
    # 在 VPS 上创建目录
    ssh "$SshUser@$VpsIp" "mkdir -p $ProjectDir"
    
    # 上传项目文件
    scp -r * "$SshUser@$VpsIp`:$ProjectDir/"
    
    # 设置权限
    ssh "$SshUser@$VpsIp" "chmod +x $ProjectDir/scripts/*.sh"
    
    Write-Info "文件上传完成"
}

# 在 VPS 上获取 SSL 证书
function Install-SslCertificate {
    if ($SkipSSL) {
        Write-Info "跳过 SSL 证书安装"
        return
    }
    
    Write-Info "设置 SSL 证书..."
    
    $sslScript = @"
cd $ProjectDir

# 安装 Certbot
apt install certbot -y

# 获取 SSL 证书
certbot certonly --standalone -d $FullDomain --email admin@$Domain --agree-tos --non-interactive

# 设置自动续期
echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -

echo "SSL 证书设置完成"
"@
    
    ssh "$SshUser@$VpsIp" $sslScript
    Write-Info "SSL 证书设置完成"
}

# 启动服务
function Start-Services {
    Write-Info "启动 Ghost 博客服务..."
    
    $startScript = @"
cd $ProjectDir

# 创建数据目录
mkdir -p data/{ghost-content,mysql-data}
mkdir -p logs/nginx

# 启动服务
docker-compose -f docker-compose.prod.yml --env-file .env.production up -d

# 等待服务启动
sleep 30

# 检查服务状态
docker-compose -f docker-compose.prod.yml ps

echo "服务启动完成"
"@
    
    ssh "$SshUser@$VpsIp" $startScript
    Write-Info "Ghost 博客服务启动完成"
}

# 配置防火墙
function Set-Firewall {
    Write-Info "配置防火墙..."
    
    $firewallScript = @"
# 安装并配置 UFW
apt install ufw -y
ufw --force reset
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw --force enable

echo "防火墙配置完成"
"@
    
    ssh "$SshUser@$VpsIp" $firewallScript
    Write-Info "防火墙配置完成"
}

# 验证部署
function Test-Deployment {
    Write-Info "验证部署..."
    
    # 检查域名解析
    try {
        $dnsResult = Resolve-DnsName $FullDomain -ErrorAction Stop
        Write-Info "域名解析正常"
    } catch {
        Write-Warn "域名 $FullDomain 解析失败，请检查 DNS 设置"
        Write-Info "请在域名管理面板添加 A 记录:"
        Write-Info "类型: A"
        Write-Info "名称: $Subdomain"
        Write-Info "值: $VpsIp"
    }
    
    # 检查 HTTP 访问
    try {
        $httpResponse = Invoke-WebRequest -Uri "http://$FullDomain" -UseBasicParsing -TimeoutSec 10
        Write-Info "HTTP 访问正常"
    } catch {
        Write-Warn "HTTP 访问失败"
    }
    
    # 检查 HTTPS 访问
    try {
        $httpsResponse = Invoke-WebRequest -Uri "https://$FullDomain" -UseBasicParsing -TimeoutSec 10
        Write-Info "HTTPS 访问正常"
    } catch {
        Write-Warn "HTTPS 访问失败，可能需要等待 SSL 证书生效"
    }
    
    Write-Info "部署验证完成"
}

# 主函数
function Main {
    Write-Info "=== Ghost 博客 VPS 部署开始 ==="
    Write-Info "域名: $FullDomain"
    Write-Info "VPS IP: $VpsIp"
    
    try {
        Test-LocalRequirements
        Test-VpsConnection
        Install-DockerOnVps
        New-ProductionConfig
        New-NginxSslConfig
        Copy-FilesToVps
        Install-SslCertificate
        Start-Services
        Set-Firewall
        Test-Deployment
        
        Write-Info "=== 部署完成 ==="
        Write-Info "博客地址: https://$FullDomain"
        Write-Info "管理后台: https://$FullDomain/ghost/"
        Write-Info ""
        Write-Info "下一步:"
        Write-Info "1. 确保域名 DNS 解析正确指向 $VpsIp"
        Write-Info "2. 访问管理后台完成初始设置"
        Write-Info "3. 配置中文内容和主题"
        Write-Info ""
        Write-Info "管理命令:"
        Write-Info "ssh $SshUser@$VpsIp 'cd $ProjectDir && docker-compose -f docker-compose.prod.yml logs -f'"
        
    } catch {
        Write-Error "部署过程中发生错误: $($_.Exception.Message)"
        exit 1
    }
}

# 执行主函数
Main

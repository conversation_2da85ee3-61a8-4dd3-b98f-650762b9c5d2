#!/bin/bash

# Ghost 博客多站点环境部署脚本
# 适用于已有其他应用的 VPS 环境
# 使用方法: ./deploy-multi-site.sh xing2006.me your-vps-ip

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -lt 2 ]; then
    log_error "使用方法: $0 <domain> <vps-ip>"
    log_info "示例: $0 xing2006.me *******"
    exit 1
fi

DOMAIN=$1
VPS_IP=$2
BLOG_DOMAIN="blog.${DOMAIN}"
PROJECT_DIR="/opt/ghost-blog-${DOMAIN//./-}"

log_info "开始多站点环境下的 Ghost 博客部署"
log_info "主域名: $DOMAIN"
log_info "博客域名: $BLOG_DOMAIN"
log_info "VPS IP: $VPS_IP"
log_info "项目目录: $PROJECT_DIR"

# 检查 VPS 连接
check_vps_connection() {
    log_info "检查 VPS 连接..."
    
    if ! ssh -o ConnectTimeout=10 root@$VPS_IP "echo 'VPS 连接成功'"; then
        log_error "无法连接到 VPS: $VPS_IP"
        exit 1
    fi
    
    log_info "VPS 连接检查通过"
}

# 检查现有服务
check_existing_services() {
    log_info "检查现有服务..."
    
    ssh root@$VPS_IP << EOF
        echo "=== 现有 Docker 容器 ==="
        docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Ports}}\t{{.Status}}"
        
        echo -e "\n=== 端口占用情况 ==="
        netstat -tlnp | grep -E ':(80|443|3306|3368|3307)' || echo "相关端口未被占用"
        
        echo -e "\n=== Nginx 配置 ==="
        if command -v nginx &> /dev/null; then
            nginx -t && echo "Nginx 配置正常"
        else
            echo "Nginx 未安装"
        fi
EOF
    
    log_info "现有服务检查完成"
}

# 创建项目目录和配置
setup_project_directory() {
    log_info "设置项目目录..."
    
    ssh root@$VPS_IP << EOF
        # 创建项目目录
        mkdir -p $PROJECT_DIR/{data/{ghost-content,mysql-data},logs/nginx,nginx/conf.d}
        
        # 设置权限
        chown -R 1000:1000 $PROJECT_DIR/data/ghost-content
        chmod -R 755 $PROJECT_DIR
        
        echo "项目目录创建完成: $PROJECT_DIR"
EOF
    
    log_info "项目目录设置完成"
}

# 上传配置文件
upload_configuration() {
    log_info "上传配置文件..."
    
    # 创建临时目录
    TEMP_DIR=$(mktemp -d)
    
    # 复制配置文件到临时目录
    cp docker-compose.multi-site.yml $TEMP_DIR/
    cp .env.xing2006 $TEMP_DIR/.env.production
    cp -r ghost/ $TEMP_DIR/ 2>/dev/null || true
    cp -r scripts/ $TEMP_DIR/ 2>/dev/null || true
    
    # 更新配置文件中的域名
    sed -i "s/xing2006.me/$DOMAIN/g" $TEMP_DIR/.env.production
    sed -i "s/blog.xing2006.me/$BLOG_DOMAIN/g" $TEMP_DIR/.env.production
    
    # 上传文件
    scp -r $TEMP_DIR/* root@$VPS_IP:$PROJECT_DIR/
    
    # 清理临时目录
    rm -rf $TEMP_DIR
    
    log_info "配置文件上传完成"
}

# 配置 Nginx 虚拟主机
setup_nginx_vhost() {
    log_info "配置 Nginx 虚拟主机..."
    
    ssh root@$VPS_IP << EOF
        # 创建虚拟主机配置
        cat > /etc/nginx/sites-available/$BLOG_DOMAIN << 'NGINX_CONFIG'
# Ghost 博客虚拟主机配置
server {
    listen 80;
    server_name $BLOG_DOMAIN;
    
    # Let's Encrypt 验证路径
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }
    
    # 重定向到 HTTPS
    location / {
        return 301 https://\$server_name\$request_uri;
    }
}

server {
    listen 443 ssl http2;
    server_name $BLOG_DOMAIN;

    # SSL 证书配置
    ssl_certificate /etc/letsencrypt/live/$BLOG_DOMAIN/fullchain.pem;
    ssl_private_key /etc/letsencrypt/live/$BLOG_DOMAIN/privkey.pem;
    
    # SSL 安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;

    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;

    # 反向代理到 Ghost
    location / {
        proxy_pass http://127.0.0.1:3368;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 缓存配置
        proxy_cache_bypass \$http_upgrade;
        proxy_buffering off;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)\$ {
        proxy_pass http://127.0.0.1:3368;
        proxy_set_header Host \$host;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
NGINX_CONFIG

        # 启用虚拟主机
        ln -sf /etc/nginx/sites-available/$BLOG_DOMAIN /etc/nginx/sites-enabled/
        
        # 测试 Nginx 配置
        nginx -t
        
        echo "Nginx 虚拟主机配置完成"
EOF
    
    log_info "Nginx 虚拟主机配置完成"
}

# 获取 SSL 证书
setup_ssl_certificate() {
    log_info "设置 SSL 证书..."
    
    ssh root@$VPS_IP << EOF
        # 确保 certbot 已安装
        if ! command -v certbot &> /dev/null; then
            apt update
            apt install certbot -y
        fi
        
        # 创建 webroot 目录
        mkdir -p /var/www/certbot
        
        # 获取 SSL 证书
        certbot certonly --webroot -w /var/www/certbot -d $BLOG_DOMAIN --email admin@$DOMAIN --agree-tos --non-interactive
        
        echo "SSL 证书获取完成"
EOF
    
    log_info "SSL 证书设置完成"
}

# 启动 Ghost 博客服务
start_ghost_service() {
    log_info "启动 Ghost 博客服务..."
    
    ssh root@$VPS_IP << EOF
        cd $PROJECT_DIR
        
        # 启动服务
        docker-compose -f docker-compose.multi-site.yml --env-file .env.production up -d
        
        # 等待服务启动
        echo "等待服务启动..."
        sleep 30
        
        # 检查服务状态
        echo "=== 服务状态 ==="
        docker-compose -f docker-compose.multi-site.yml ps
        
        echo -e "\n=== 端口检查 ==="
        netstat -tlnp | grep -E ':(3368|3307)'
        
        echo "Ghost 博客服务启动完成"
EOF
    
    log_info "Ghost 博客服务启动完成"
}

# 重载 Nginx 配置
reload_nginx() {
    log_info "重载 Nginx 配置..."
    
    ssh root@$VPS_IP << EOF
        # 测试配置
        nginx -t
        
        # 重载配置（不会影响现有站点）
        nginx -s reload
        
        echo "Nginx 配置重载完成"
EOF
    
    log_info "Nginx 配置重载完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 检查域名解析
    if nslookup $BLOG_DOMAIN > /dev/null 2>&1; then
        log_info "域名解析正常"
    else
        log_warn "域名 $BLOG_DOMAIN 解析失败，请检查 DNS 设置"
        log_info "请在域名管理面板添加 A 记录:"
        log_info "类型: A"
        log_info "名称: blog"
        log_info "值: $VPS_IP"
    fi
    
    # 检查服务访问
    ssh root@$VPS_IP << EOF
        echo "=== 服务访问测试 ==="
        
        # 测试 Ghost 直接访问
        if curl -f http://localhost:3368 > /dev/null 2>&1; then
            echo "✅ Ghost 直接访问正常"
        else
            echo "❌ Ghost 直接访问失败"
        fi
        
        # 测试域名访问
        if curl -f https://$BLOG_DOMAIN > /dev/null 2>&1; then
            echo "✅ HTTPS 访问正常"
        else
            echo "❌ HTTPS 访问失败"
        fi
        
        echo -e "\n=== 现有服务检查 ==="
        if curl -f https://$DOMAIN > /dev/null 2>&1; then
            echo "✅ 主站点访问正常"
        else
            echo "⚠️  主站点访问异常，请检查"
        fi
EOF
    
    log_info "部署验证完成"
}

# 主函数
main() {
    log_info "=== Ghost 博客多站点部署开始 ==="
    
    check_vps_connection
    check_existing_services
    setup_project_directory
    upload_configuration
    setup_nginx_vhost
    setup_ssl_certificate
    start_ghost_service
    reload_nginx
    verify_deployment
    
    log_info "=== 部署完成 ==="
    log_info "博客地址: https://$BLOG_DOMAIN"
    log_info "管理后台: https://$BLOG_DOMAIN/ghost/"
    log_info ""
    log_info "管理命令:"
    log_info "ssh root@$VPS_IP 'cd $PROJECT_DIR && docker-compose -f docker-compose.multi-site.yml logs -f'"
    log_info ""
    log_info "注意事项:"
    log_info "1. 确保域名 DNS 解析正确指向 $VPS_IP"
    log_info "2. 现有应用应该不受影响"
    log_info "3. Ghost 博客使用独立的端口 3368 和 3307"
    log_info "4. 可以独立管理和更新 Ghost 博客"
}

# 执行主函数
main "$@"

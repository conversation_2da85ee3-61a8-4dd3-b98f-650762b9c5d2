# 数据库连接测试脚本 (PowerShell 版本)
# 用于验证 MySQL 数据库连接是否正常

param(
    [switch]$Verbose
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 日志函数
function Write-Info {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warn {
    param($Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

try {
    # 加载环境变量
    if (Test-Path ".env") {
        Write-Info "加载环境变量..."
        Get-Content ".env" | ForEach-Object {
            if ($_ -match "^([^#][^=]+)=(.*)$") {
                [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
            }
        }
    } else {
        Write-Error ".env 文件不存在"
        exit 1
    }

    # 获取环境变量
    $MYSQL_USER = $env:MYSQL_USER
    $MYSQL_PASSWORD = $env:MYSQL_PASSWORD
    $MYSQL_DATABASE = $env:MYSQL_DATABASE

    # 检查必要的环境变量
    if (-not $MYSQL_USER -or -not $MYSQL_PASSWORD -or -not $MYSQL_DATABASE) {
        Write-Error "缺少必要的数据库环境变量"
        exit 1
    }

    Write-Info "开始测试数据库连接..."

    # 等待 MySQL 容器启动
    Write-Info "等待 MySQL 容器启动..."
    $maxAttempts = 30
    $attempt = 1

    while ($attempt -le $maxAttempts) {
        try {
            $result = docker-compose exec -T mysql mysqladmin ping -h localhost --silent 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Info "MySQL 容器已启动"
                break
            }
        } catch {
            # 忽略错误，继续尝试
        }
        
        Write-Warn "等待 MySQL 容器启动... (尝试 $attempt/$maxAttempts)"
        Start-Sleep -Seconds 2
        $attempt++
    }

    if ($attempt -gt $maxAttempts) {
        Write-Error "MySQL 容器启动超时"
        exit 1
    }

    # 测试数据库连接
    Write-Info "测试数据库连接..."
    $testQuery = "SELECT 1;"
    $result = docker-compose exec -T mysql mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -h localhost "$MYSQL_DATABASE" -e "$testQuery" 2>$null
    
    if ($LASTEXITCODE -eq 0) {
        Write-Info "数据库连接成功"
    } else {
        Write-Error "数据库连接失败"
        exit 1
    }

    # 测试数据库表
    Write-Info "检查测试表..."
    $tableQuery = "SELECT * FROM connection_test LIMIT 1;"
    $result = docker-compose exec -T mysql mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -h localhost "$MYSQL_DATABASE" -e "$tableQuery" 2>$null
    
    if ($LASTEXITCODE -eq 0) {
        Write-Info "测试表存在且可访问"
        
        # 显示测试表内容
        Write-Info "测试表内容："
        docker-compose exec -T mysql mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -h localhost "$MYSQL_DATABASE" -e "SELECT * FROM connection_test;"
    } else {
        Write-Warn "测试表不存在或无法访问"
    }

    # 显示数据库信息
    Write-Info "数据库信息："
    $infoQuery = @"
SELECT 
    DATABASE() as current_database,
    USER() as current_user,
    VERSION() as mysql_version,
    @@character_set_database as charset,
    @@collation_database as collation;
"@
    
    docker-compose exec -T mysql mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -h localhost "$MYSQL_DATABASE" -e "$infoQuery"

    Write-Info "数据库连接测试完成"

} catch {
    Write-Error "测试过程中发生错误: $($_.Exception.Message)"
    exit 1
}

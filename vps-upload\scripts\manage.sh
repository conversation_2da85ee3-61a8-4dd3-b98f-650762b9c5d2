#!/bin/bash

# Ghost 博客管理脚本
# 提供常用的管理操作

set -e

PROJECT_DIR="/opt/ghost-blog-xing2006"

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Ghost 博客管理脚本"
    echo
    echo "使用方法: $0 <命令>"
    echo
    echo "可用命令:"
    echo "  start     - 启动服务"
    echo "  stop      - 停止服务"
    echo "  restart   - 重启服务"
    echo "  status    - 查看状态"
    echo "  logs      - 查看日志"
    echo "  update    - 更新服务"
    echo "  backup    - 备份数据"
    echo "  monitor   - 监控服务"
    echo "  shell     - 进入容器"
    echo "  cleanup   - 清理资源"
    echo "  help      - 显示帮助"
    echo
    echo "示例:"
    echo "  $0 start"
    echo "  $0 logs ghost"
    echo "  $0 shell ghost"
}

# 启动服务
start_service() {
    log_info "启动 Ghost 博客服务..."
    cd "$PROJECT_DIR"
    docker-compose -f docker-compose.multi-site.yml --env-file .env.xing2006 up -d
    log_info "服务启动完成"
}

# 停止服务
stop_service() {
    log_info "停止 Ghost 博客服务..."
    cd "$PROJECT_DIR"
    docker-compose -f docker-compose.multi-site.yml down
    log_info "服务停止完成"
}

# 重启服务
restart_service() {
    log_info "重启 Ghost 博客服务..."
    cd "$PROJECT_DIR"
    docker-compose -f docker-compose.multi-site.yml restart
    log_info "服务重启完成"
}

# 查看状态
show_status() {
    log_info "Ghost 博客服务状态:"
    cd "$PROJECT_DIR"
    docker-compose -f docker-compose.multi-site.yml ps
    
    echo
    echo "端口监听状态:"
    netstat -tlnp | grep -E ':(3368|3307)' || echo "相关端口未监听"
}

# 查看日志
show_logs() {
    cd "$PROJECT_DIR"
    
    if [ -n "$1" ]; then
        log_info "查看 $1 服务日志..."
        docker-compose -f docker-compose.multi-site.yml logs -f "$1"
    else
        log_info "查看所有服务日志..."
        docker-compose -f docker-compose.multi-site.yml logs -f
    fi
}

# 更新服务
update_service() {
    log_info "更新 Ghost 博客服务..."
    cd "$PROJECT_DIR"
    
    # 拉取最新镜像
    docker-compose -f docker-compose.multi-site.yml pull
    
    # 重新启动服务
    docker-compose -f docker-compose.multi-site.yml up -d
    
    log_info "服务更新完成"
}

# 备份数据
backup_data() {
    log_info "开始备份数据..."
    if [ -f "$PROJECT_DIR/scripts/backup.sh" ]; then
        bash "$PROJECT_DIR/scripts/backup.sh"
    else
        log_error "备份脚本不存在"
    fi
}

# 监控服务
monitor_service() {
    log_info "开始监控服务..."
    if [ -f "$PROJECT_DIR/scripts/monitor.sh" ]; then
        bash "$PROJECT_DIR/scripts/monitor.sh"
    else
        log_error "监控脚本不存在"
    fi
}

# 进入容器
enter_shell() {
    cd "$PROJECT_DIR"
    
    if [ -n "$1" ]; then
        SERVICE="$1"
    else
        SERVICE="ghost"
    fi
    
    log_info "进入 $SERVICE 容器..."
    
    if [ "$SERVICE" = "ghost" ]; then
        docker-compose -f docker-compose.multi-site.yml exec ghost sh
    elif [ "$SERVICE" = "mysql" ]; then
        docker-compose -f docker-compose.multi-site.yml exec mysql mysql -u ghost_user_xing2006 -p
    else
        docker-compose -f docker-compose.multi-site.yml exec "$SERVICE" sh
    fi
}

# 清理资源
cleanup_resources() {
    log_info "清理 Docker 资源..."
    
    # 清理未使用的镜像
    docker image prune -f
    
    # 清理未使用的卷
    docker volume prune -f
    
    # 清理未使用的网络
    docker network prune -f
    
    log_info "资源清理完成"
}

# 主函数
main() {
    if [ $# -eq 0 ]; then
        show_help
        exit 1
    fi
    
    case "$1" in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs "$2"
            ;;
        update)
            update_service
            ;;
        backup)
            backup_data
            ;;
        monitor)
            monitor_service
            ;;
        shell)
            enter_shell "$2"
            ;;
        cleanup)
            cleanup_resources
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"

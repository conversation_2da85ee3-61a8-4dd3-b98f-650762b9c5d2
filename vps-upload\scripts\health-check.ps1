# 容器健康检查脚本 (PowerShell 版本)
# 用于检查各个服务的健康状态

param(
    [switch]$Verbose,
    [switch]$ShowResources
)

# 设置错误处理
$ErrorActionPreference = "Continue"

# 日志函数
function Write-Info {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warn {
    param($Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Debug {
    param($Message)
    if ($Verbose) {
        Write-Host "[DEBUG] $Message" -ForegroundColor Blue
    }
}

# 检查服务是否运行
function Test-ServiceRunning {
    param(
        [string]$ServiceName,
        [string]$ContainerName
    )
    
    Write-Info "检查 $ServiceName 服务状态..."
    
    try {
        $containers = docker ps --format "{{.Names}}"
        if ($containers -contains $ContainerName) {
            Write-Info "$ServiceName 容器正在运行"
            return $true
        } else {
            Write-Error "$ServiceName 容器未运行"
            return $false
        }
    } catch {
        Write-Error "检查 $ServiceName 容器状态时发生错误: $($_.Exception.Message)"
        return $false
    }
}

# 检查 MySQL 健康状态
function Test-MySQLHealth {
    Write-Info "检查 MySQL 数据库健康状态..."
    
    if (-not (Test-ServiceRunning "MySQL" "ghost_mysql")) {
        return $false
    }
    
    try {
        # 检查 MySQL 连接
        $result = docker exec ghost_mysql mysqladmin ping -h localhost --silent 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Info "MySQL 数据库连接正常"
            
            # 检查数据库是否可以查询
            $result = docker exec ghost_mysql mysql -u root -p"$env:MYSQL_ROOT_PASSWORD" -e "SELECT 1;" 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Info "MySQL 数据库查询正常"
                return $true
            } else {
                Write-Error "MySQL 数据库查询失败"
                return $false
            }
        } else {
            Write-Error "MySQL 数据库连接失败"
            return $false
        }
    } catch {
        Write-Error "检查 MySQL 健康状态时发生错误: $($_.Exception.Message)"
        return $false
    }
}

# 检查 Ghost 应用健康状态
function Test-GhostHealth {
    Write-Info "检查 Ghost 应用健康状态..."
    
    if (-not (Test-ServiceRunning "Ghost" "ghost_app")) {
        return $false
    }
    
    try {
        # 检查 Ghost 应用端口
        $result = docker exec ghost_app netstat -tlnp 2>$null | Select-String ":2368"
        if ($result) {
            Write-Info "Ghost 应用端口监听正常"
        } else {
            Write-Error "Ghost 应用端口未监听"
            return $false
        }
        
        # 检查 Ghost 应用 HTTP 响应
        $result = docker exec ghost_app wget --quiet --tries=1 --spider http://localhost:2368/ 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Info "Ghost 应用 HTTP 响应正常"
            return $true
        } else {
            Write-Error "Ghost 应用 HTTP 响应失败"
            return $false
        }
    } catch {
        Write-Error "检查 Ghost 健康状态时发生错误: $($_.Exception.Message)"
        return $false
    }
}

# 检查 Nginx 健康状态
function Test-NginxHealth {
    Write-Info "检查 Nginx 反向代理健康状态..."
    
    if (-not (Test-ServiceRunning "Nginx" "ghost_nginx")) {
        return $false
    }
    
    try {
        # 检查 Nginx 配置
        $result = docker exec ghost_nginx nginx -t 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Info "Nginx 配置文件正确"
        } else {
            Write-Error "Nginx 配置文件有错误"
            return $false
        }
        
        # 检查 Nginx 端口
        $result = docker exec ghost_nginx netstat -tlnp 2>$null | Select-String ":80"
        if ($result) {
            Write-Info "Nginx 端口监听正常"
        } else {
            Write-Error "Nginx 端口未监听"
            return $false
        }
        
        # 检查通过 Nginx 访问 Ghost
        try {
            $response = Invoke-WebRequest -Uri "http://localhost/" -UseBasicParsing -TimeoutSec 10
            if ($response.StatusCode -eq 200) {
                Write-Info "通过 Nginx 访问 Ghost 正常"
                return $true
            } else {
                Write-Error "通过 Nginx 访问 Ghost 返回状态码: $($response.StatusCode)"
                return $false
            }
        } catch {
            Write-Error "通过 Nginx 访问 Ghost 失败: $($_.Exception.Message)"
            return $false
        }
    } catch {
        Write-Error "检查 Nginx 健康状态时发生错误: $($_.Exception.Message)"
        return $false
    }
}

# 检查容器资源使用情况
function Get-ResourceUsage {
    Write-Info "检查容器资源使用情况..."
    
    try {
        $stats = docker stats --no-stream --format "table {{.Container}}`t{{.CPUPerc}}`t{{.MemUsage}}`t{{.NetIO}}`t{{.BlockIO}}" ghost_mysql ghost_app ghost_nginx 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host $stats
        } else {
            Write-Warn "无法获取容器统计信息"
        }
    } catch {
        Write-Warn "获取容器资源使用情况时发生错误: $($_.Exception.Message)"
    }
}

# 检查磁盘空间
function Get-DiskSpace {
    Write-Info "检查磁盘空间..."
    
    try {
        Write-Host "Docker 系统使用情况:"
        docker system df
        
        Write-Host "`n主机磁盘空间:"
        Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 } | 
            Select-Object DeviceID, @{Name="Size(GB)";Expression={[math]::Round($_.Size/1GB,2)}}, 
                         @{Name="FreeSpace(GB)";Expression={[math]::Round($_.FreeSpace/1GB,2)}}, 
                         @{Name="PercentFree";Expression={[math]::Round(($_.FreeSpace/$_.Size)*100,2)}} |
            Format-Table -AutoSize
    } catch {
        Write-Warn "获取磁盘空间信息时发生错误: $($_.Exception.Message)"
    }
}

# 主函数
function Main {
    Write-Info "开始系统健康检查..."
    
    $exitCode = 0
    
    # 加载环境变量
    if (Test-Path ".env") {
        Get-Content ".env" | ForEach-Object {
            if ($_ -match "^([^#][^=]+)=(.*)$") {
                [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
            }
        }
    } else {
        Write-Error ".env 文件不存在"
        return 1
    }
    
    # 检查各个服务
    if (-not (Test-MySQLHealth)) { $exitCode = 1 }
    Write-Host ""
    
    if (-not (Test-GhostHealth)) { $exitCode = 1 }
    Write-Host ""
    
    if (-not (Test-NginxHealth)) { $exitCode = 1 }
    Write-Host ""
    
    # 检查资源使用情况
    if ($ShowResources) {
        Get-ResourceUsage
        Write-Host ""
        
        Get-DiskSpace
        Write-Host ""
    }
    
    if ($exitCode -eq 0) {
        Write-Info "所有服务健康检查通过"
    } else {
        Write-Error "部分服务健康检查失败"
    }
    
    return $exitCode
}

# 执行主函数
try {
    $result = Main
    exit $result
} catch {
    Write-Error "健康检查过程中发生错误: $($_.Exception.Message)"
    exit 1
}

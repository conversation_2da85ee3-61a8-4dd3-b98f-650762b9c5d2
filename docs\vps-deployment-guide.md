# Ghost 博客 VPS 部署和二级域名配置指南

本指南详细说明如何在 VPS 上部署 Ghost 博客并配置二级域名访问。

## 🚀 部署概览

### 部署架构
```
域名解析: blog.yourdomain.com → VPS IP
VPS: Nginx (80/443) → Ghost (2368) → MySQL (3306)
SSL: Let's Encrypt 自动证书
```

### 前置要求
- VPS 服务器（推荐 2GB+ RAM）
- 域名（如 yourdomain.com）
- SSH 访问权限

## 📋 第一步：准备 VPS 环境

### 1.1 连接到 VPS
```bash
ssh root@your-vps-ip
# 或使用密钥
ssh -i your-key.pem user@your-vps-ip
```

### 1.2 更新系统
```bash
# Ubuntu/Debian
apt update && apt upgrade -y

# CentOS/RHEL
yum update -y
```

### 1.3 安装必要软件
```bash
# 安装 Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装 Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# 验证安装
docker --version
docker-compose --version
```

## 🌐 第二步：配置域名解析

### 2.1 添加 DNS 记录
在您的域名管理面板中添加 A 记录：

```
类型: A
名称: blog (或您想要的二级域名)
值: your-vps-ip
TTL: 300 (或默认)
```

### 2.2 验证 DNS 解析
```bash
# 在本地测试
nslookup blog.yourdomain.com
# 或
dig blog.yourdomain.com
```

## 📁 第三步：部署项目文件

### 3.1 创建项目目录
```bash
mkdir -p /opt/ghost-blog
cd /opt/ghost-blog
```

### 3.2 上传项目文件
方法一：使用 Git（推荐）
```bash
git clone https://github.com/your-username/ghost-blog-docker.git .
```

方法二：使用 SCP 上传
```bash
# 在本地执行
scp -r /path/to/ghost-blog-docker/* root@your-vps-ip:/opt/ghost-blog/
```

### 3.3 设置文件权限
```bash
chmod +x scripts/*.sh
chmod +x scripts/*.ps1
```

## ⚙️ 第四步：配置环境变量

### 4.1 创建生产环境配置
```bash
cp .env.example .env.production
```

### 4.2 编辑生产环境配置
```bash
nano .env.production
```

配置内容：
```env
# MySQL 数据库配置
MYSQL_ROOT_PASSWORD=your_secure_root_password_2024
MYSQL_DATABASE=ghost_production
MYSQL_USER=ghost_user
MYSQL_PASSWORD=your_secure_ghost_password_2024

# Ghost 应用配置
GHOST_URL=https://blog.yourdomain.com
GHOST_LOCALE=zh
GHOST_ADMIN_EMAIL=<EMAIL>

# 邮件配置
MAIL_FROM=<EMAIL>
MAIL_HOST=smtp.yourdomain.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password

# SSL 配置
SSL_EMAIL=<EMAIL>
DOMAIN_NAME=blog.yourdomain.com
```

## 🔧 第五步：修改配置文件

### 5.1 更新 Ghost 配置
编辑 `ghost/config.production.json`：
```json
{
  "url": "https://blog.yourdomain.com",
  "server": {
    "port": 2368,
    "host": "0.0.0.0"
  },
  "i18n": {
    "defaultLocale": "zh"
  },
  "database": {
    "client": "mysql",
    "connection": {
      "host": "mysql",
      "port": 3306,
      "user": "ghost_user",
      "password": "your_secure_ghost_password_2024",
      "database": "ghost_production",
      "charset": "utf8mb4"
    }
  },
  "mail": {
    "transport": "SMTP",
    "options": {
      "service": "your-email-service",
      "auth": {
        "user": "<EMAIL>",
        "pass": "your_email_password"
      }
    }
  }
}
```

### 5.2 配置 Nginx SSL
创建 `nginx/ssl.conf`：
```nginx
server {
    listen 80;
    server_name blog.yourdomain.com;
    
    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name blog.yourdomain.com;

    # SSL 证书配置
    ssl_certificate /etc/letsencrypt/live/blog.yourdomain.com/fullchain.pem;
    ssl_private_key /etc/letsencrypt/live/blog.yourdomain.com/privkey.pem;
    
    # SSL 安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;

    # 反向代理到 Ghost
    location / {
        proxy_pass http://ghost:2368;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 缓存配置
        proxy_cache_bypass $http_upgrade;
        proxy_buffering off;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://ghost:2368;
        proxy_set_header Host $host;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 🔒 第六步：配置 SSL 证书

### 6.1 安装 Certbot
```bash
# Ubuntu/Debian
apt install certbot python3-certbot-nginx -y

# CentOS/RHEL
yum install certbot python3-certbot-nginx -y
```

### 6.2 获取 SSL 证书
```bash
certbot certonly --standalone -d blog.yourdomain.com --email <EMAIL> --agree-tos --non-interactive
```

### 6.3 配置自动续期
```bash
# 添加到 crontab
echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
```

## 🚀 第七步：启动服务

### 7.1 创建生产环境 Docker Compose
创建 `docker-compose.prod.yml`：
```yaml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: ghost_mysql_prod
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data_prod:/var/lib/mysql
    networks:
      - ghost_network_prod
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  ghost:
    image: ghost:5-alpine
    container_name: ghost_app_prod
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      database__client: mysql
      database__connection__host: mysql
      database__connection__user: ${MYSQL_USER}
      database__connection__password: ${MYSQL_PASSWORD}
      database__connection__database: ${MYSQL_DATABASE}
      url: ${GHOST_URL}
    volumes:
      - ghost_content_prod:/var/lib/ghost/content
      - ./ghost/config.production.json:/var/lib/ghost/config.production.json:ro
    networks:
      - ghost_network_prod
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:2368/"]
      timeout: 30s
      retries: 3
      interval: 60s

  nginx:
    image: nginx:alpine
    container_name: ghost_nginx_prod
    restart: unless-stopped
    depends_on:
      - ghost
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/ssl.conf:/etc/nginx/conf.d/default.conf:ro
      - /etc/letsencrypt:/etc/letsencrypt:ro
    networks:
      - ghost_network_prod
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      timeout: 15s
      retries: 3
      interval: 30s

volumes:
  ghost_content_prod:
  mysql_data_prod:

networks:
  ghost_network_prod:
    driver: bridge
```

### 7.2 启动生产环境
```bash
# 使用生产环境配置
docker-compose -f docker-compose.prod.yml --env-file .env.production up -d

# 查看状态
docker-compose -f docker-compose.prod.yml ps

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f
```

## 🔥 第八步：配置防火墙

### 8.1 配置 UFW（Ubuntu）
```bash
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw enable
```

### 8.2 配置 firewalld（CentOS）
```bash
firewall-cmd --permanent --add-service=ssh
firewall-cmd --permanent --add-service=http
firewall-cmd --permanent --add-service=https
firewall-cmd --reload
```

## ✅ 第九步：验证部署

### 9.1 检查服务状态
```bash
# 检查容器状态
docker-compose -f docker-compose.prod.yml ps

# 检查端口监听
netstat -tlnp | grep -E ':(80|443|2368|3306)'

# 检查 SSL 证书
openssl s_client -connect blog.yourdomain.com:443 -servername blog.yourdomain.com
```

### 9.2 访问测试
- HTTP: http://blog.yourdomain.com（应自动跳转到 HTTPS）
- HTTPS: https://blog.yourdomain.com
- 管理后台: https://blog.yourdomain.com/ghost/

## 🛠️ 第十步：生产环境优化

### 10.1 性能优化
```bash
# 设置系统参数
echo 'vm.max_map_count=262144' >> /etc/sysctl.conf
echo 'fs.file-max=65536' >> /etc/sysctl.conf
sysctl -p
```

### 10.2 监控脚本
创建监控脚本 `scripts/monitor-prod.sh`：
```bash
#!/bin/bash
# 生产环境监控脚本

echo "=== Ghost 博客生产环境状态 ==="
echo "时间: $(date)"
echo

echo "容器状态:"
docker-compose -f docker-compose.prod.yml ps

echo
echo "磁盘使用:"
df -h

echo
echo "内存使用:"
free -h

echo
echo "SSL 证书状态:"
certbot certificates

echo
echo "网站可访问性:"
curl -I https://blog.yourdomain.com
```

### 10.3 备份脚本
创建备份脚本 `scripts/backup-prod.sh`：
```bash
#!/bin/bash
# 生产环境备份脚本

BACKUP_DIR="/opt/backups/ghost-$(date +%Y%m%d-%H%M%S)"
mkdir -p $BACKUP_DIR

echo "开始备份 Ghost 博客..."

# 备份数据库
docker-compose -f docker-compose.prod.yml exec -T mysql mysqldump -u ghost_user -p'your_password' ghost_production > $BACKUP_DIR/database.sql

# 备份 Ghost 内容
docker run --rm -v ghost_content_prod:/data -v $BACKUP_DIR:/backup alpine tar czf /backup/ghost-content.tar.gz -C /data .

# 备份配置文件
cp -r ghost/ nginx/ .env.production docker-compose.prod.yml $BACKUP_DIR/

echo "备份完成: $BACKUP_DIR"
```

## 📚 常用管理命令

```bash
# 查看日志
docker-compose -f docker-compose.prod.yml logs -f ghost

# 重启服务
docker-compose -f docker-compose.prod.yml restart

# 更新镜像
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d

# 进入容器
docker-compose -f docker-compose.prod.yml exec ghost sh

# 备份数据
./scripts/backup-prod.sh

# 监控状态
./scripts/monitor-prod.sh
```

## 🎉 部署完成

现在您可以通过 `https://blog.yourdomain.com` 访问您的 Ghost 博客了！

记住：
- 定期备份数据
- 监控服务状态  
- 及时更新系统和应用
- 查看日志排查问题

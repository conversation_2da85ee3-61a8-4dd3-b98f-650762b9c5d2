# SSL 证书配置

此目录用于存放 SSL 证书文件，以启用 HTTPS 访问。

## 文件结构

```
ssl/
├── cert.pem      # SSL 证书文件
├── privkey.pem   # 私钥文件
└── chain.pem     # 证书链文件（可选）
```

## 配置步骤

1. 将 SSL 证书文件放置在此目录中
2. 更新 `.env` 文件中的 SSL 配置：
   ```
   SSL_ENABLED=true
   SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
   SSL_KEY_PATH=/etc/nginx/ssl/privkey.pem
   ```
3. 重新启动服务：`docker-compose restart nginx`

## 获取免费 SSL 证书

推荐使用 Let's Encrypt 获取免费 SSL 证书：

```bash
# 使用 certbot 获取证书
certbot certonly --standalone -d yourdomain.com

# 复制证书到项目目录
cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem ./ssl/cert.pem
cp /etc/letsencrypt/live/yourdomain.com/privkey.pem ./ssl/privkey.pem
```

## 注意事项

- 确保证书文件权限正确（600 或 644）
- 定期更新证书以避免过期
- 在生产环境中使用有效的域名证书

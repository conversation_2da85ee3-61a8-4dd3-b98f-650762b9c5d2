# 设计文档

## 概述

个人博客网站将基于 Ghost 博客平台构建，使用 Docker 容器化部署。Ghost 是一个专业的博客发布平台，提供现代化的编辑器、主题系统和完整的博客功能。整个系统将通过 docker-compose 编排，包含 Ghost 应用、MySQL 数据库和 Nginx 反向代理。

## 架构

### 系统架构图

```mermaid
graph TB
    A[用户浏览器] --> B[Nginx 反向代理]
    B --> C[Ghost 博客应用]
    C --> D[MySQL 数据库]
    
    E[Docker Host] --> F[Ghost Container]
    E --> G[MySQL Container]
    E --> H[Nginx Container]
    
    I[持久化存储] --> J[博客内容数据]
    I --> K[数据库数据]
    I --> L[上传文件]
```

### 技术栈选择

- **博客平台**: Ghost 5.x (Node.js 基础)
- **数据库**: MySQL 8.0
- **反向代理**: Nginx 1.24
- **容器化**: Docker + Docker Compose
- **持久化**: Docker Volumes

## 组件和接口

### 1. Ghost 博客应用容器

**职责**:
- 提供博客内容管理界面
- 处理文章的创建、编辑、发布
- 提供 RESTful API
- 处理用户认证和会话管理

**配置**:
- 端口: 2368 (内部)
- 环境变量: 数据库连接、URL配置
- 挂载点: 内容目录、主题目录

### 2. MySQL 数据库容器

**职责**:
- 存储博客文章、用户信息、配置数据
- 提供数据持久化
- 处理数据库事务和查询

**配置**:
- 端口: 3306 (内部)
- 数据卷: mysql_data
- 环境变量: 数据库名、用户名、密码

### 3. Nginx 反向代理容器

**职责**:
- 处理 HTTP/HTTPS 请求
- 静态文件服务
- SSL 终止 (可选)
- 负载均衡和缓存

**配置**:
- 端口: 80, 443 (外部)
- 配置文件: nginx.conf
- SSL 证书挂载 (可选)

## 数据模型

### Ghost 内置数据模型

Ghost 使用以下核心数据模型：

1. **Posts (文章)**
   - id, title, slug, content
   - status (draft/published)
   - created_at, updated_at, published_at
   - author_id, featured

2. **Users (用户)**
   - id, name, email, password_hash
   - role (owner/admin/editor/author)
   - created_at, updated_at

3. **Tags (标签)**
   - id, name, slug, description
   - created_at, updated_at

4. **Posts_Tags (文章标签关联)**
   - post_id, tag_id

### 文件存储结构

```
/var/lib/ghost/content/
├── apps/           # 应用扩展
├── data/           # 数据导入导出
├── images/         # 上传的图片
├── logs/           # 日志文件
├── settings/       # 配置文件
└── themes/         # 主题文件
```

## 错误处理

### 1. 应用级错误处理

- **数据库连接失败**: 自动重试机制，显示维护页面
- **文件上传错误**: 文件大小限制，格式验证
- **认证失败**: 登录重试限制，会话超时处理

### 2. 容器级错误处理

- **容器健康检查**: 定期检查应用响应状态
- **自动重启策略**: 容器异常时自动重启
- **资源限制**: 内存和CPU使用限制

### 3. 网络错误处理

- **反向代理超时**: 合理的超时设置
- **SSL证书过期**: 证书自动更新机制
- **DNS解析失败**: 备用DNS配置

## 测试策略

### 1. 单元测试

- Ghost 内置测试套件
- 自定义主题组件测试
- 配置文件验证测试

### 2. 集成测试

- 容器间通信测试
- 数据库连接测试
- 文件上传功能测试

### 3. 端到端测试

- 用户注册登录流程
- 文章创建发布流程
- 前端页面渲染测试

### 4. 性能测试

- 页面加载时间测试
- 数据库查询性能测试
- 并发用户访问测试

### 5. 安全测试

- 认证授权测试
- SQL注入防护测试
- XSS攻击防护测试

## 部署配置

### Docker Compose 服务定义

1. **ghost 服务**
   - 镜像: ghost:5-alpine
   - 环境变量: 数据库配置、URL设置
   - 依赖: mysql 服务
   - 卷挂载: 内容目录

2. **mysql 服务**
   - 镜像: mysql:8.0
   - 环境变量: 数据库初始化配置
   - 卷挂载: 数据持久化

3. **nginx 服务**
   - 镜像: nginx:alpine
   - 端口映射: 80:80, 443:443
   - 配置文件挂载
   - 依赖: ghost 服务

### 环境配置

- **开发环境**: 单容器部署，SQLite数据库
- **生产环境**: 多容器编排，MySQL数据库，SSL配置
- **备份策略**: 定期数据库备份，内容文件备份

### 监控和日志

- **应用日志**: Ghost 内置日志系统
- **访问日志**: Nginx 访问和错误日志
- **系统监控**: Docker 容器状态监控
- **性能指标**: 响应时间、内存使用、磁盘空间
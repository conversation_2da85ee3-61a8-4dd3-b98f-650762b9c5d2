-- Ghost 数据库初始化脚本
-- 创建 Ghost 数据库和用户

-- 确保使用 UTF8MB4 字符集
ALTER DATABASE ghost_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建 Ghost 用户并授权 (如果不存在)
CREATE USER IF NOT EXISTS 'ghost_user'@'%' IDENTIFIED BY 'ghost_password_2024';
GRANT ALL PRIVILEGES ON ghost_production.* TO 'ghost_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 设置 MySQL 配置以优化 Ghost 性能
-- innodb_file_format 在 MySQL 8.0 中已移除，默认使用 Barracuda
-- innodb_file_per_table 在 MySQL 8.0 中默认为 ON
-- innodb_large_prefix 在 MySQL 8.0 中已移除，默认启用

-- 创建一个测试表来验证连接
CREATE TABLE IF NOT EXISTS connection_test (
    id INT AUTO_INCREMENT PRIMARY KEY,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    message VARCHAR(255) DEFAULT 'Database connection successful'
);

INSERT INTO connection_test (message) VALUES ('Ghost database initialized successfully');

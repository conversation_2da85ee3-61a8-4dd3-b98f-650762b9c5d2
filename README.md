# Ghost 博客 Docker 部署

基于 Docker 的 Ghost 博客平台，包含 Ghost 应用、MySQL 数据库和 Nginx 反向代理的完整解决方案。

## 功能特性

- 🚀 一键部署 Ghost 博客
- 🗄️ MySQL 8.0 数据库持久化
- 🌐 Nginx 反向代理和静态文件优化
- 🔒 安全配置和健康检查
- 📊 性能优化和缓存策略
- 🛠️ 完整的管理脚本

## 系统要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少 2GB 可用内存
- 至少 5GB 可用磁盘空间

## 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd ghost-blog-docker
```

### 2. 配置环境变量

复制并编辑环境变量文件：

```bash
cp .env.example .env
```

编辑 `.env` 文件，设置数据库密码和其他配置：

```env
MYSQL_ROOT_PASSWORD=your_secure_root_password
MYSQL_PASSWORD=your_secure_password
GHOST_URL=http://your-domain.com
```

### 3. 启动服务

使用部署脚本启动所有服务：

```powershell
# Windows PowerShell
.\scripts\deploy.ps1 -Action start

# 或使用 Docker Compose
docker-compose up -d
```

### 4. 访问博客

- 博客首页: http://localhost
- 管理后台: http://localhost/ghost

## 项目结构

```
.
├── docker-compose.yml          # Docker Compose 配置
├── .env                        # 环境变量配置
├── ghost/
│   └── config.production.json  # Ghost 应用配置
├── nginx/
│   ├── nginx.conf             # Nginx 主配置
│   └── conf.d/
│       └── ghost.conf         # Ghost 站点配置
├── mysql/
│   └── init/
│       └── 01-init-ghost-db.sql # 数据库初始化脚本
├── scripts/
│   ├── deploy.ps1             # 部署脚本 (PowerShell)
│   ├── deploy.sh              # 部署脚本 (Bash)
│   ├── test-db-connection.ps1 # 数据库测试脚本 (PowerShell)
│   └── test-db-connection.sh  # 数据库测试脚本 (Bash)
└── ssl/                       # SSL 证书目录
```

## 管理命令

### 使用部署脚本

```powershell
# 启动服务
.\scripts\deploy.ps1 -Action start

# 停止服务
.\scripts\deploy.ps1 -Action stop

# 重启服务
.\scripts\deploy.ps1 -Action restart

# 查看状态
.\scripts\deploy.ps1 -Action status

# 查看日志
.\scripts\deploy.ps1 -Action logs

# 更新服务
.\scripts\deploy.ps1 -Action update
```

### 使用 Docker Compose

```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 查看日志
docker-compose logs -f

# 重启特定服务
docker-compose restart ghost
```

## 数据备份

### 数据库备份

```bash
# 备份数据库
docker-compose exec mysql mysqldump -u ghost_user -p ghost_production > backup.sql

# 恢复数据库
docker-compose exec -T mysql mysql -u ghost_user -p ghost_production < backup.sql
```

### 内容文件备份

```bash
# 备份 Ghost 内容
docker run --rm -v ghost_content:/data -v $(pwd):/backup alpine tar czf /backup/ghost-content.tar.gz -C /data .

# 恢复 Ghost 内容
docker run --rm -v ghost_content:/data -v $(pwd):/backup alpine tar xzf /backup/ghost-content.tar.gz -C /data
```

## 故障排除

### 常见问题

1. **容器启动失败**
   - 检查端口是否被占用
   - 查看容器日志：`docker-compose logs`

2. **数据库连接失败**
   - 运行数据库测试脚本：`.\scripts\test-db-connection.ps1`
   - 检查环境变量配置

3. **Ghost 无法访问**
   - 确认所有容器都在运行：`docker-compose ps`
   - 检查 Nginx 配置：`docker-compose exec nginx nginx -t`

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs ghost
docker-compose logs mysql
docker-compose logs nginx
```

## 性能优化

- Nginx 静态文件缓存
- Ghost 应用缓存
- MySQL 查询优化
- 图片自动压缩

## 安全配置

- 数据库用户权限限制
- Nginx 安全头设置
- 容器网络隔离
- 健康检查和自动重启

## 🔧 高级功能

### 健康检查和监控
```bash
# 运行健康检查
.\scripts\health-check.ps1 -ShowResources

# 启动服务监控
.\scripts\monitor-services.ps1
```

### 自动化测试
```bash
# 运行集成测试
.\tests\integration-tests.ps1 -Verbose
```

### 服务管理脚本
- `health-check.ps1`: 系统健康检查
- `monitor-services.ps1`: 自动服务监控
- `integration-tests.ps1`: 完整集成测试

## 📚 文档

- [部署指南](docs/deployment-guide.md) - 详细的部署和配置说明
- [用户指南](docs/user-guide.md) - Ghost 博客使用教程
- [故障排除](docs/troubleshooting.md) - 常见问题解决方案

## 🎉 部署成功！

如果您看到这个文档，说明 Ghost 博客已经成功部署！

- 🌐 **博客首页**: http://localhost
- ⚙️ **管理后台**: http://localhost/ghost
- 📖 **完整文档**: [docs/](docs/)

开始您的博客之旅吧！ 🚀

## 许可证

MIT License

version: '3.8'

services:
  # MySQL 数据库服务 - 使用独立端口和网络
  mysql:
    image: mysql:8.0
    container_name: ghost_mysql_xing2006
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data_xing2006:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d:ro
    networks:
      - ghost_network_xing2006
    ports:
      - "3307:3306"  # 使用不同端口避免与现有数据库冲突
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
      interval: 30s
      start_period: 60s
    command: --default-authentication-plugin=mysql_native_password
    security_opt:
      - no-new-privileges:true

  # Ghost 博客应用 - 使用独立端口
  ghost:
    image: ghost:5-alpine
    container_name: ghost_app_xing2006
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      # 数据库配置
      database__client: mysql
      database__connection__host: mysql
      database__connection__user: ${MYSQL_USER}
      database__connection__password: ${MYSQL_PASSWORD}
      database__connection__database: ${MYSQL_DATABASE}
      # Ghost 配置
      url: ${GHOST_URL}
      NODE_ENV: production
    volumes:
      - ghost_content_xing2006:/var/lib/ghost/content
      - ./ghost/config.production.json:/var/lib/ghost/config.production.json:ro
    networks:
      - ghost_network_xing2006
    ports:
      - "3368:2368"  # 使用独立端口，避免与现有应用冲突
    healthcheck:
      test: ["CMD", "sh", "-c", "wget --quiet --tries=1 --spider http://localhost:2368/ || exit 1"]
      timeout: 30s
      retries: 3
      interval: 60s
      start_period: 120s
    security_opt:
      - no-new-privileges:true
    user: "1000:1000"

# 数据卷 - 使用独立命名避免冲突
volumes:
  ghost_content_xing2006:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/ghost-blog-xing2006/data/ghost-content
  mysql_data_xing2006:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/ghost-blog-xing2006/data/mysql-data

# 网络 - 使用独立网络避免与现有应用冲突
networks:
  ghost_network_xing2006:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16  # 使用不同的子网段

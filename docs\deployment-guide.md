# Ghost 博客 Docker 部署指南

## 概述

本项目使用 Docker Compose 部署一个完整的 Ghost 博客系统，包含以下组件：

- **Ghost 5.130**: 博客应用主体
- **MySQL 8.0**: 数据库服务
- **Nginx**: 反向代理和静态文件服务
- **健康检查**: 自动监控和重启机制

## 系统要求

### 硬件要求
- CPU: 2 核心或以上
- 内存: 4GB RAM 或以上
- 存储: 20GB 可用空间或以上
- 网络: 稳定的互联网连接

### 软件要求
- Docker 20.10 或以上版本
- Docker Compose 2.0 或以上版本
- Windows 10/11 或 Linux 系统

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd ghost-blog-docker
```

### 2. 配置环境变量
复制环境变量模板并根据需要修改：
```bash
cp .env.example .env
```

编辑 `.env` 文件，设置以下关键参数：
```env
# MySQL 数据库配置
MYSQL_ROOT_PASSWORD=your_secure_root_password
MYSQL_DATABASE=ghost_production
MYSQL_USER=ghost
MYSQL_PASSWORD=your_secure_ghost_password

# Ghost 应用配置
GHOST_URL=http://localhost
GHOST_ADMIN_EMAIL=<EMAIL>
GHOST_ADMIN_PASSWORD=your_secure_admin_password

# 邮件配置（可选）
MAIL_FROM=<EMAIL>
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
```

### 3. 启动服务
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 4. 访问博客
- 博客首页: http://localhost
- 管理后台: http://localhost/ghost

## 服务架构

### 网络架构
```
Internet → Nginx (80/443) → Ghost (2368) → MySQL (3306)
```

### 数据持久化
- `ghost_content`: Ghost 内容数据（主题、图片、配置）
- `mysql_data`: MySQL 数据库文件

### 健康检查
所有服务都配置了健康检查：
- **MySQL**: 每30秒检查数据库连接
- **Ghost**: 每60秒检查HTTP响应
- **Nginx**: 每30秒检查配置和代理状态

## 管理命令

### 服务管理
```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看服务状态
docker-compose ps

# 查看资源使用情况
docker stats
```

### 日志管理
```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs ghost
docker-compose logs mysql
docker-compose logs nginx

# 实时跟踪日志
docker-compose logs -f
```

### 数据备份
```bash
# 备份 MySQL 数据库
docker-compose exec mysql mysqldump -u root -p ghost_production > backup.sql

# 备份 Ghost 内容
docker run --rm -v ghost_content:/data -v $(pwd):/backup alpine tar czf /backup/ghost-content-backup.tar.gz -C /data .
```

### 数据恢复
```bash
# 恢复 MySQL 数据库
docker-compose exec -T mysql mysql -u root -p ghost_production < backup.sql

# 恢复 Ghost 内容
docker run --rm -v ghost_content:/data -v $(pwd):/backup alpine tar xzf /backup/ghost-content-backup.tar.gz -C /data
```

## 监控和维护

### 健康检查脚本
项目提供了自动化健康检查脚本：

```bash
# PowerShell 版本（Windows）
.\scripts\health-check.ps1 -ShowResources

# Bash 版本（Linux）
./scripts/health-check.sh
```

### 服务监控
启动自动监控服务：
```bash
# 启动监控（每60秒检查一次）
.\scripts\monitor-services.ps1

# 仅检查模式（不执行重启）
.\scripts\monitor-services.ps1 -DryRun

# 自定义检查间隔
.\scripts\monitor-services.ps1 -CheckInterval 120
```

### 性能优化
1. **内存优化**: 根据服务器配置调整容器内存限制
2. **缓存配置**: Nginx 已配置静态文件缓存
3. **数据库优化**: MySQL 配置了适当的缓冲区大小
4. **日志轮转**: 配置了日志文件自动轮转

## 故障排除

### 常见问题

#### 1. 端口冲突
**问题**: 端口 80、443 或 2368 被占用
**解决**: 
- 检查占用端口的进程: `netstat -ano | findstr :80`
- 修改 docker-compose.yml 中的端口映射

#### 2. 数据库连接失败
**问题**: Ghost 无法连接到 MySQL
**解决**:
- 检查 MySQL 容器状态: `docker-compose logs mysql`
- 验证数据库凭据配置
- 确保 MySQL 容器完全启动后再启动 Ghost

#### 3. 权限问题
**问题**: 文件权限导致的启动失败
**解决**:
- 检查数据卷权限: `docker-compose exec ghost ls -la /var/lib/ghost/content`
- 重置权限: `docker-compose exec ghost chown -R ghost:ghost /var/lib/ghost/content`

#### 4. 内存不足
**问题**: 容器因内存不足被杀死
**解决**:
- 增加系统内存或调整容器内存限制
- 检查内存使用: `docker stats`

### 调试模式
启用详细日志记录：
```bash
# 设置 Ghost 调试模式
docker-compose exec ghost ghost config set logging.level debug

# 重启服务以应用更改
docker-compose restart ghost
```

## 安全建议

1. **更改默认密码**: 确保所有默认密码都已更改
2. **使用 HTTPS**: 在生产环境中配置 SSL 证书
3. **防火墙配置**: 只开放必要的端口
4. **定期更新**: 保持 Docker 镜像和系统更新
5. **备份策略**: 建立定期备份机制
6. **访问控制**: 限制管理后台访问

## 生产环境部署

### SSL/TLS 配置
1. 获取 SSL 证书（Let's Encrypt 推荐）
2. 修改 Nginx 配置以启用 HTTPS
3. 更新 Ghost URL 配置为 HTTPS

### 域名配置
1. 配置 DNS 记录指向服务器 IP
2. 更新 `.env` 文件中的 `GHOST_URL`
3. 重启服务以应用更改

### 性能调优
1. 启用 Nginx gzip 压缩
2. 配置 CDN（如 Cloudflare）
3. 优化数据库查询缓存
4. 配置 Redis 缓存（可选）

## 支持和帮助

如果遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查项目 GitHub Issues
3. 查看 Ghost 官方文档
4. 联系技术支持团队

## 相关文档

- [用户使用指南](user-guide.md)
- [故障排除文档](troubleshooting.md)
- [维护和更新指南](maintenance-guide.md)
- [API 文档](api-documentation.md)

---

**版本**: 1.0.0
**最后更新**: 2025-07-26
**维护者**: Ghost Blog Team

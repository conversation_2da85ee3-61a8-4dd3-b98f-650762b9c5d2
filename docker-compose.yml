version: '3.8'

services:
  # MySQL 数据库服务
  mysql:
    image: mysql:8.0
    container_name: ghost_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - ghost_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
      interval: 30s

  # Ghost 博客应用服务
  ghost:
    image: ghost:5-alpine
    container_name: ghost_app
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      # Ghost 基本配置
      url: ${GHOST_URL}
      NODE_ENV: production
      
      # 数据库配置
      database__client: mysql
      database__connection__host: mysql
      database__connection__port: 3306
      database__connection__user: ${MYSQL_USER}
      database__connection__password: ${MYSQL_PASSWORD}
      database__connection__database: ${MYSQL_DATABASE}
      
      # 邮件配置 (可选)
      mail__transport: SMTP
      mail__options__service: ${MAIL_SERVICE:-}
      mail__options__auth__user: ${MAIL_USER:-}
      mail__options__auth__pass: ${MAIL_PASSWORD:-}
      
      # 安全配置
      privacy__useUpdateCheck: false
      privacy__useGravatar: false
    ports:
      - "2368:2368"
    volumes:
      - ghost_content:/var/lib/ghost/content
      - ./ghost/config.production.json:/var/lib/ghost/config.production.json:ro
    networks:
      - ghost_network
    healthcheck:
      test: ["CMD", "sh", "-c", "wget --quiet --tries=1 --spider http://localhost:2368/ || exit 1"]
      timeout: 30s
      retries: 3
      interval: 60s
      start_period: 120s

  # Nginx 反向代理服务
  nginx:
    image: nginx:alpine
    container_name: ghost_nginx
    restart: unless-stopped
    depends_on:
      - ghost
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ghost_content:/var/lib/ghost/content:ro
    networks:
      - ghost_network
    healthcheck:
      test: ["CMD", "sh", "-c", "nginx -t && wget --quiet --tries=1 --spider http://localhost/nginx-health || exit 1"]
      timeout: 15s
      retries: 3
      interval: 30s
      start_period: 30s

volumes:
  mysql_data:
    driver: local
  ghost_content:
    driver: local

networks:
  ghost_network:
    driver: bridge

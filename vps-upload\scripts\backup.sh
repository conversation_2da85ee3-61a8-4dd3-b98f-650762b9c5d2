#!/bin/bash

# Ghost 博客备份脚本
# 用于备份数据库和内容文件

set -e

# 配置
BACKUP_DIR="/opt/backups/ghost-xing2006"
PROJECT_DIR="/opt/ghost-blog-xing2006"
DATE=$(date +%Y%m%d-%H%M%S)
BACKUP_NAME="ghost-backup-$DATE"

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建备份目录
create_backup_dir() {
    log_info "创建备份目录..."
    mkdir -p "$BACKUP_DIR/$BACKUP_NAME"
    cd "$PROJECT_DIR"
}

# 备份数据库
backup_database() {
    log_info "备份数据库..."
    
    if docker-compose -f docker-compose.multi-site.yml ps | grep -q "ghost_mysql_xing2006.*Up"; then
        docker-compose -f docker-compose.multi-site.yml exec -T mysql \
            mysqldump -u ghost_user_xing2006 -p'ghost_password_xing2006_2024' \
            ghost_production_xing2006 > "$BACKUP_DIR/$BACKUP_NAME/database.sql"
        
        log_info "数据库备份完成"
    else
        log_error "MySQL 容器未运行"
        return 1
    fi
}

# 备份 Ghost 内容
backup_ghost_content() {
    log_info "备份 Ghost 内容..."
    
    if [ -d "data/ghost-content" ]; then
        tar -czf "$BACKUP_DIR/$BACKUP_NAME/ghost-content.tar.gz" -C data ghost-content
        log_info "Ghost 内容备份完成"
    else
        log_warn "Ghost 内容目录不存在"
    fi
}

# 备份配置文件
backup_config() {
    log_info "备份配置文件..."
    
    cp docker-compose.multi-site.yml "$BACKUP_DIR/$BACKUP_NAME/"
    cp .env.xing2006 "$BACKUP_DIR/$BACKUP_NAME/"
    cp -r ghost/ "$BACKUP_DIR/$BACKUP_NAME/" 2>/dev/null || true
    cp -r nginx/ "$BACKUP_DIR/$BACKUP_NAME/" 2>/dev/null || true
    
    log_info "配置文件备份完成"
}

# 创建备份信息文件
create_backup_info() {
    log_info "创建备份信息..."
    
    cat > "$BACKUP_DIR/$BACKUP_NAME/backup-info.txt" << EOF
Ghost 博客备份信息
==================

备份时间: $(date)
备份名称: $BACKUP_NAME
项目目录: $PROJECT_DIR

包含内容:
- database.sql          # 数据库备份
- ghost-content.tar.gz  # Ghost 内容文件
- docker-compose.multi-site.yml  # Docker 配置
- .env.xing2006        # 环境变量
- ghost/               # Ghost 配置
- nginx/               # Nginx 配置

恢复方法:
1. 解压 ghost-content.tar.gz 到 data/ 目录
2. 导入 database.sql 到数据库
3. 复制配置文件到项目目录
4. 重启服务

EOF
}

# 压缩备份
compress_backup() {
    log_info "压缩备份文件..."
    
    cd "$BACKUP_DIR"
    tar -czf "$BACKUP_NAME.tar.gz" "$BACKUP_NAME"
    rm -rf "$BACKUP_NAME"
    
    log_info "备份压缩完成: $BACKUP_DIR/$BACKUP_NAME.tar.gz"
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理旧备份..."
    
    # 保留最近 7 天的备份
    find "$BACKUP_DIR" -name "ghost-backup-*.tar.gz" -mtime +7 -delete
    
    log_info "旧备份清理完成"
}

# 显示备份大小
show_backup_size() {
    if [ -f "$BACKUP_DIR/$BACKUP_NAME.tar.gz" ]; then
        SIZE=$(du -h "$BACKUP_DIR/$BACKUP_NAME.tar.gz" | cut -f1)
        log_info "备份文件大小: $SIZE"
    fi
}

# 主函数
main() {
    log_info "=== Ghost 博客备份开始 ==="
    
    create_backup_dir
    backup_database
    backup_ghost_content
    backup_config
    create_backup_info
    compress_backup
    cleanup_old_backups
    show_backup_size
    
    log_info "=== 备份完成 ==="
    log_info "备份文件: $BACKUP_DIR/$BACKUP_NAME.tar.gz"
}

# 执行备份
main "$@"

# VPS 上传文件包说明

## 📁 文件结构

这个文件夹包含了在 VPS 上部署 Ghost 博客所需的所有文件：

```
vps-upload/
├── README.md                           # 本说明文件
├── docker-compose.multi-site.yml      # Docker Compose 配置文件
├── .env.xing2006                      # 环境变量配置
├── ghost/
│   └── config.production.json         # Ghost 生产环境配置
├── nginx/
│   └── nginx.conf                     # Nginx 基础配置
├── mysql/
│   └── init/
│       └── init.sql                   # MySQL 初始化脚本
└── scripts/
    ├── deploy-multi-site.sh           # 自动部署脚本
    ├── backup.sh                      # 备份脚本
    ├── monitor.sh                     # 监控脚本
    └── manage.sh                      # 管理脚本

```

## 🚀 上传和部署步骤

### 1. 上传文件到 VPS
```bash
# 将整个 vps-upload 文件夹上传到 VPS
scp -r vps-upload/* root@your-vps-ip:/opt/ghost-blog-xing2006/
```

### 2. 在 VPS 上执行部署
```bash
# 连接到 VPS
ssh root@your-vps-ip

# 进入项目目录
cd /opt/ghost-blog-xing2006

# 给脚本执行权限
chmod +x scripts/*.sh

# 执行部署
./scripts/deploy-multi-site.sh xing2006.me your-vps-ip
```

### 3. 手动部署（如果自动脚本失败）
```bash
# 创建数据目录
mkdir -p data/{ghost-content,mysql-data} logs/nginx
chown -R 1000:1000 data/ghost-content

# 启动服务
docker-compose -f docker-compose.multi-site.yml --env-file .env.xing2006 up -d

# 配置 Nginx（参考 MULTI-SITE-QUICK-DEPLOY.md）
```

## ⚙️ 配置说明

### 环境变量 (.env.xing2006)
- 包含数据库密码、域名等配置
- 上传后可根据实际情况修改

### Docker Compose (docker-compose.multi-site.yml)
- 使用独立端口：Ghost(3368), MySQL(3307)
- 使用独立网络：**********/16
- 避免与现有应用冲突

### Ghost 配置 (ghost/config.production.json)
- 生产环境配置
- 中文语言设置
- 数据库连接配置

## 🔧 部署后管理

### 查看服务状态
```bash
docker-compose -f docker-compose.multi-site.yml ps
```

### 查看日志
```bash
docker-compose -f docker-compose.multi-site.yml logs -f
```

### 备份数据
```bash
./scripts/backup.sh
```

### 监控服务
```bash
./scripts/monitor.sh
```

## 📝 注意事项

1. **DNS 配置**：确保 `blog.xing2006.me` 解析到您的 VPS IP
2. **防火墙**：确保端口 80、443 开放
3. **SSL 证书**：部署脚本会自动获取 Let's Encrypt 证书
4. **现有应用**：不会影响您现有的应用

## 🎯 访问地址

部署完成后：
- 博客首页：https://blog.xing2006.me
- 管理后台：https://blog.xing2006.me/ghost/

## 🆘 故障排除

如果遇到问题，请查看：
- `MULTI-SITE-QUICK-DEPLOY.md` - 详细部署指南
- `docs/multi-site-deployment.md` - 完整技术文档

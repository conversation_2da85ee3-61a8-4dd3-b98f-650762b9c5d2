# VPS 上传文件清单

## 📋 文件清单

### 核心配置文件
- ✅ `docker-compose.multi-site.yml` - Docker Compose 配置
- ✅ `.env.xing2006` - 环境变量配置
- ✅ `README.md` - 部署说明
- ✅ `MULTI-SITE-QUICK-DEPLOY.md` - 快速部署指南

### Ghost 配置
- ✅ `ghost/config.production.json` - Ghost 生产环境配置

### Nginx 配置
- ✅ `nginx/nginx.conf` - Nginx 基础配置

### MySQL 配置
- ✅ `mysql/init/init.sql` - MySQL 初始化脚本

### 管理脚本
- ✅ `scripts/deploy-multi-site.sh` - 自动部署脚本
- ✅ `scripts/backup.sh` - 备份脚本
- ✅ `scripts/monitor.sh` - 监控脚本
- ✅ `scripts/manage.sh` - 管理脚本
- ✅ 其他辅助脚本

## 🚀 上传步骤

### 1. 压缩文件夹（可选）
```bash
# 在本地创建压缩包
tar -czf ghost-blog-vps.tar.gz vps-upload/
```

### 2. 上传到 VPS
```bash
# 方法一：直接上传文件夹
scp -r vps-upload/* root@your-vps-ip:/opt/ghost-blog-xing2006/

# 方法二：上传压缩包后解压
scp ghost-blog-vps.tar.gz root@your-vps-ip:/tmp/
ssh root@your-vps-ip "cd /opt && tar -xzf /tmp/ghost-blog-vps.tar.gz && mv vps-upload ghost-blog-xing2006"
```

### 3. 设置权限
```bash
ssh root@your-vps-ip "chmod +x /opt/ghost-blog-xing2006/scripts/*.sh"
```

## ⚙️ 部署前检查

### DNS 配置
确保已添加 DNS 记录：
```
类型: A
名称: blog
值: your-vps-ip
TTL: 300
```

### VPS 环境
确保 VPS 已安装：
- Docker
- Docker Compose
- Nginx
- Certbot

### 端口检查
确保以下端口未被占用：
- 3368 (Ghost)
- 3307 (MySQL)

## 🎯 部署命令

### 自动部署
```bash
cd /opt/ghost-blog-xing2006
./scripts/deploy-multi-site.sh xing2006.me your-vps-ip
```

### 手动部署
```bash
cd /opt/ghost-blog-xing2006

# 1. 创建数据目录
mkdir -p data/{ghost-content,mysql-data} logs/nginx
chown -R 1000:1000 data/ghost-content

# 2. 启动服务
docker-compose -f docker-compose.multi-site.yml --env-file .env.xing2006 up -d

# 3. 配置 Nginx 虚拟主机
# （参考 MULTI-SITE-QUICK-DEPLOY.md）

# 4. 获取 SSL 证书
certbot certonly --webroot -w /var/www/certbot -d blog.xing2006.me

# 5. 重载 Nginx
nginx -s reload
```

## 🔧 常用管理命令

```bash
cd /opt/ghost-blog-xing2006

# 查看状态
./scripts/manage.sh status

# 查看日志
./scripts/manage.sh logs

# 备份数据
./scripts/manage.sh backup

# 监控服务
./scripts/manage.sh monitor

# 重启服务
./scripts/manage.sh restart
```

## 📞 访问地址

部署完成后：
- 博客首页：https://blog.xing2006.me
- 管理后台：https://blog.xing2006.me/ghost/

## ⚠️ 注意事项

1. **不会影响现有应用**：使用独立端口和网络
2. **数据安全**：定期备份数据
3. **SSL 证书**：自动获取和续期
4. **监控**：定期检查服务状态

## 🆘 故障排除

如果遇到问题：
1. 检查 DNS 解析：`nslookup blog.xing2006.me`
2. 检查端口占用：`netstat -tlnp | grep -E ':(3368|3307)'`
3. 查看容器日志：`./scripts/manage.sh logs`
4. 检查 Nginx 配置：`nginx -t`

## 📚 相关文档

- `README.md` - 详细说明
- `MULTI-SITE-QUICK-DEPLOY.md` - 快速部署指南
- `docs/multi-site-deployment.md` - 完整技术文档（如果需要）

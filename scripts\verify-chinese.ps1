# Ghost 博客中文设置验证脚本

param(
    [string]$GhostUrl = "http://localhost"
)

Write-Host "=== Ghost 博客中文设置验证 ===" -ForegroundColor Green

# 检查前台页面语言设置
Write-Host "`n1. 检查前台页面语言设置..." -ForegroundColor Yellow
try {
    $frontendResponse = Invoke-WebRequest -Uri $GhostUrl -UseBasicParsing -TimeoutSec 10
    if ($frontendResponse.Content -match 'lang="([^"]+)"') {
        $language = $matches[1]
        if ($language -eq "zh" -or $language -eq "zh-CN") {
            Write-Host "   ✅ 前台语言设置正确: $language" -ForegroundColor Green
        } else {
            Write-Host "   ❌ 前台语言设置错误: $language (应该是 zh 或 zh-CN)" -ForegroundColor Red
        }
    } else {
        Write-Host "   ❌ 未找到语言设置" -ForegroundColor Red
    }
} catch {
    Write-Host "   ❌ 无法访问前台页面: $($_.Exception.Message)" -ForegroundColor Red
}

# 检查管理后台
Write-Host "`n2. 检查管理后台..." -ForegroundColor Yellow
try {
    $adminResponse = Invoke-WebRequest -Uri "$GhostUrl/ghost/" -UseBasicParsing -TimeoutSec 10
    if ($adminResponse.StatusCode -eq 200) {
        Write-Host "   ✅ 管理后台可访问" -ForegroundColor Green
    } else {
        Write-Host "   ❌ 管理后台访问异常，状态码: $($adminResponse.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "   ❌ 无法访问管理后台: $($_.Exception.Message)" -ForegroundColor Red
}

# 检查 Ghost 配置文件
Write-Host "`n3. 检查 Ghost 配置文件..." -ForegroundColor Yellow
if (Test-Path "ghost/config.production.json") {
    $config = Get-Content "ghost/config.production.json" | ConvertFrom-Json
    if ($config.i18n -and $config.i18n.defaultLocale) {
        $configLocale = $config.i18n.defaultLocale
        if ($configLocale -eq "zh" -or $configLocale -eq "zh-CN") {
            Write-Host "   ✅ 配置文件语言设置正确: $configLocale" -ForegroundColor Green
        } else {
            Write-Host "   ❌ 配置文件语言设置错误: $configLocale" -ForegroundColor Red
        }
    } else {
        Write-Host "   ❌ 配置文件中未找到语言设置" -ForegroundColor Red
    }
} else {
    Write-Host "   ❌ 配置文件不存在" -ForegroundColor Red
}

# 检查环境变量
Write-Host "`n4. 检查环境变量..." -ForegroundColor Yellow
if (Test-Path ".env") {
    $envContent = Get-Content ".env"
    $localeEnv = $envContent | Where-Object { $_ -match "^GHOST_LOCALE=" }
    if ($localeEnv) {
        $envLocale = ($localeEnv -split "=")[1]
        if ($envLocale -eq "zh" -or $envLocale -eq "zh-CN") {
            Write-Host "   ✅ 环境变量语言设置正确: $envLocale" -ForegroundColor Green
        } else {
            Write-Host "   ❌ 环境变量语言设置错误: $envLocale" -ForegroundColor Red
        }
    } else {
        Write-Host "   ⚠️  环境变量中未设置 GHOST_LOCALE" -ForegroundColor Yellow
    }
} else {
    Write-Host "   ❌ .env 文件不存在" -ForegroundColor Red
}

# 检查容器状态
Write-Host "`n5. 检查容器状态..." -ForegroundColor Yellow
try {
    $containerStatus = docker-compose ps --format json | ConvertFrom-Json
    $ghostContainer = $containerStatus | Where-Object { $_.Service -eq "ghost" }
    if ($ghostContainer -and $ghostContainer.State -eq "running") {
        Write-Host "   ✅ Ghost 容器正在运行" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Ghost 容器未运行或状态异常" -ForegroundColor Red
    }
} catch {
    Write-Host "   ❌ 无法检查容器状态: $($_.Exception.Message)" -ForegroundColor Red
}

# 提供建议
Write-Host "`n=== 建议 ===" -ForegroundColor Cyan
Write-Host "1. 如果语言设置未生效，请尝试以下步骤：" -ForegroundColor White
Write-Host "   - 清除浏览器缓存" -ForegroundColor Gray
Write-Host "   - 重启 Ghost 服务: docker-compose restart ghost" -ForegroundColor Gray
Write-Host "   - 通过管理后台手动设置语言" -ForegroundColor Gray

Write-Host "`n2. 访问链接：" -ForegroundColor White
Write-Host "   - 博客首页: $GhostUrl" -ForegroundColor Gray
Write-Host "   - 管理后台: $GhostUrl/ghost/" -ForegroundColor Gray

Write-Host "`n3. 如需进一步中文化，请参考：" -ForegroundColor White
Write-Host "   - docs/chinese-localization.md" -ForegroundColor Gray

Write-Host "`n验证完成！" -ForegroundColor Green

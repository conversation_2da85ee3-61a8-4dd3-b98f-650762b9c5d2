# Ghost 博客部署脚本 (PowerShell 版本)
# 一键部署和管理 Ghost 博客系统

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("start", "stop", "restart", "status", "logs", "update")]
    [string]$Action = "start",
    
    [switch]$Build,
    [switch]$Verbose
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 日志函数
function Write-Info {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warn {
    param($Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Success {
    param($Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Cyan
}

# 检查 Docker 和 Docker Compose
function Test-Prerequisites {
    Write-Info "检查系统依赖..."
    
    try {
        $dockerVersion = docker --version
        Write-Info "Docker: $dockerVersion"
    } catch {
        Write-Error "Docker 未安装或未启动"
        exit 1
    }
    
    try {
        $composeVersion = docker-compose --version
        Write-Info "Docker Compose: $composeVersion"
    } catch {
        Write-Error "Docker Compose 未安装"
        exit 1
    }
}

# 检查配置文件
function Test-Configuration {
    Write-Info "检查配置文件..."
    
    $requiredFiles = @(
        "docker-compose.yml",
        ".env",
        "ghost/config.production.json",
        "nginx/nginx.conf",
        "nginx/conf.d/ghost.conf"
    )
    
    foreach ($file in $requiredFiles) {
        if (-not (Test-Path $file)) {
            Write-Error "缺少配置文件: $file"
            exit 1
        }
    }
    
    Write-Info "所有配置文件检查通过"
}

# 启动服务
function Start-Services {
    Write-Info "启动 Ghost 博客服务..."
    
    if ($Build) {
        Write-Info "重新构建镜像..."
        docker-compose build --no-cache
    }
    
    Write-Info "启动容器..."
    docker-compose up -d
    
    Write-Info "等待服务启动..."
    Start-Sleep -Seconds 10
    
    # 检查服务状态
    $services = docker-compose ps --services
    foreach ($service in $services) {
        $status = docker-compose ps $service
        Write-Info "服务 $service 状态: $status"
    }
    
    Write-Success "Ghost 博客已启动"
    Write-Info "访问地址: http://localhost"
    Write-Info "管理后台: http://localhost/ghost"
}

# 停止服务
function Stop-Services {
    Write-Info "停止 Ghost 博客服务..."
    docker-compose down
    Write-Success "服务已停止"
}

# 重启服务
function Restart-Services {
    Write-Info "重启 Ghost 博客服务..."
    docker-compose restart
    Write-Success "服务已重启"
}

# 查看服务状态
function Get-ServiceStatus {
    Write-Info "Ghost 博客服务状态:"
    docker-compose ps
    
    Write-Info "`n容器资源使用情况:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
}

# 查看日志
function Get-ServiceLogs {
    Write-Info "显示服务日志..."
    docker-compose logs --tail=50 -f
}

# 更新服务
function Update-Services {
    Write-Info "更新 Ghost 博客服务..."
    
    Write-Info "拉取最新镜像..."
    docker-compose pull
    
    Write-Info "重新创建容器..."
    docker-compose up -d --force-recreate
    
    Write-Success "服务已更新"
}

# 主函数
function Main {
    Write-Info "Ghost 博客部署脚本"
    Write-Info "操作: $Action"
    
    # 检查先决条件
    Test-Prerequisites
    Test-Configuration
    
    # 执行操作
    switch ($Action) {
        "start" {
            Start-Services
        }
        "stop" {
            Stop-Services
        }
        "restart" {
            Restart-Services
        }
        "status" {
            Get-ServiceStatus
        }
        "logs" {
            Get-ServiceLogs
        }
        "update" {
            Update-Services
        }
        default {
            Write-Error "未知操作: $Action"
            exit 1
        }
    }
}

# 错误处理
try {
    Main
} catch {
    Write-Error "部署过程中发生错误: $($_.Exception.Message)"
    exit 1
}

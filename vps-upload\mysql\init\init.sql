-- Ghost 博客数据库初始化脚本

-- 设置字符集
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS ghost_production_xing2006 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE ghost_production_xing2006;

-- 创建用户（如果不存在）
CREATE USER IF NOT EXISTS 'ghost_user_xing2006'@'%' IDENTIFIED BY 'ghost_password_xing2006_2024';

-- 授权
GRANT ALL PRIVILEGES ON ghost_production_xing2006.* TO 'ghost_user_xing2006'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 设置时区
SET time_zone = '+08:00';

-- 优化配置
SET GLOBAL innodb_buffer_pool_size = 134217728; -- 128MB
SET GLOBAL innodb_log_file_size = 67108864;     -- 64MB
SET GLOBAL max_connections = 200;
SET GLOBAL query_cache_size = 16777216;         -- 16MB

-- 创建一些基础表（Ghost 会自动创建，这里只是预设）
-- Ghost 会在首次启动时自动创建所有必要的表

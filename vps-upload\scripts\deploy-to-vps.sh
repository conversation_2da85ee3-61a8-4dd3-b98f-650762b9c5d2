#!/bin/bash

# Ghost 博客 VPS 自动部署脚本
# 使用方法: ./deploy-to-vps.sh your-domain.com your-vps-ip

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查参数
if [ $# -lt 2 ]; then
    log_error "使用方法: $0 <domain> <vps-ip> [subdomain]"
    log_info "示例: $0 yourdomain.com ******* blog"
    exit 1
fi

DOMAIN=$1
VPS_IP=$2
SUBDOMAIN=${3:-blog}
FULL_DOMAIN="${SUBDOMAIN}.${DOMAIN}"

log_info "开始部署 Ghost 博客到 VPS"
log_info "域名: $FULL_DOMAIN"
log_info "VPS IP: $VPS_IP"

# 检查本地环境
check_local_requirements() {
    log_info "检查本地环境..."
    
    if ! command -v ssh &> /dev/null; then
        log_error "SSH 未安装"
        exit 1
    fi
    
    if ! command -v scp &> /dev/null; then
        log_error "SCP 未安装"
        exit 1
    fi
    
    log_info "本地环境检查通过"
}

# 检查 VPS 连接
check_vps_connection() {
    log_info "检查 VPS 连接..."
    
    if ! ssh -o ConnectTimeout=10 root@$VPS_IP "echo 'VPS 连接成功'"; then
        log_error "无法连接到 VPS: $VPS_IP"
        log_info "请确保:"
        log_info "1. VPS IP 地址正确"
        log_info "2. SSH 密钥已配置"
        log_info "3. 防火墙允许 SSH 连接"
        exit 1
    fi
    
    log_info "VPS 连接检查通过"
}

# 在 VPS 上安装 Docker
install_docker_on_vps() {
    log_info "在 VPS 上安装 Docker..."
    
    ssh root@$VPS_IP << 'EOF'
        # 更新系统
        apt update && apt upgrade -y
        
        # 安装 Docker
        if ! command -v docker &> /dev/null; then
            curl -fsSL https://get.docker.com -o get-docker.sh
            sh get-docker.sh
            rm get-docker.sh
        fi
        
        # 安装 Docker Compose
        if ! command -v docker-compose &> /dev/null; then
            curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
            chmod +x /usr/local/bin/docker-compose
        fi
        
        # 启动 Docker 服务
        systemctl enable docker
        systemctl start docker
        
        echo "Docker 安装完成"
        docker --version
        docker-compose --version
EOF
    
    log_info "Docker 安装完成"
}

# 创建生产环境配置
create_production_config() {
    log_info "创建生产环境配置..."
    
    # 生成随机密码
    MYSQL_ROOT_PASSWORD=$(openssl rand -base64 32)
    MYSQL_PASSWORD=$(openssl rand -base64 32)
    
    # 创建生产环境变量文件
    cat > .env.production << EOF
# MySQL 数据库配置
MYSQL_ROOT_PASSWORD=$MYSQL_ROOT_PASSWORD
MYSQL_DATABASE=ghost_production
MYSQL_USER=ghost_user
MYSQL_PASSWORD=$MYSQL_PASSWORD

# Ghost 应用配置
GHOST_URL=https://$FULL_DOMAIN
GHOST_LOCALE=zh
GHOST_ADMIN_EMAIL=admin@$DOMAIN

# SSL 配置
SSL_EMAIL=admin@$DOMAIN
DOMAIN_NAME=$FULL_DOMAIN
EOF
    
    # 更新 Ghost 配置文件
    cat > ghost/config.production.json << EOF
{
  "url": "https://$FULL_DOMAIN",
  "server": {
    "port": 2368,
    "host": "0.0.0.0"
  },
  "i18n": {
    "defaultLocale": "zh"
  },
  "database": {
    "client": "mysql",
    "connection": {
      "host": "mysql",
      "port": 3306,
      "user": "ghost_user",
      "password": "$MYSQL_PASSWORD",
      "database": "ghost_production",
      "charset": "utf8mb4"
    },
    "pool": {
      "min": 2,
      "max": 10
    }
  },
  "mail": {
    "transport": "Direct",
    "options": {}
  },
  "logging": {
    "level": "info",
    "rotation": {
      "enabled": true,
      "count": 15,
      "period": "1d"
    },
    "transports": [
      "file",
      "stdout"
    ]
  },
  "process": "systemd",
  "paths": {
    "contentPath": "/var/lib/ghost/content"
  },
  "privacy": {
    "useUpdateCheck": false,
    "useGravatar": false,
    "useRpcPing": false,
    "useStructuredData": true
  },
  "imageOptimization": {
    "resize": true,
    "srcsets": true
  },
  "compress": true,
  "caching": {
    "frontend": {
      "maxAge": 600
    },
    "301": {
      "maxAge": 31536000
    }
  }
}
EOF
    
    log_info "生产环境配置创建完成"
}

# 创建 Nginx SSL 配置
create_nginx_ssl_config() {
    log_info "创建 Nginx SSL 配置..."
    
    mkdir -p nginx/conf.d
    
    cat > nginx/conf.d/ssl.conf << EOF
server {
    listen 80;
    server_name $FULL_DOMAIN;
    
    # Let's Encrypt 验证
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }
    
    # 重定向到 HTTPS
    location / {
        return 301 https://\$server_name\$request_uri;
    }
}

server {
    listen 443 ssl http2;
    server_name $FULL_DOMAIN;

    # SSL 证书配置
    ssl_certificate /etc/letsencrypt/live/$FULL_DOMAIN/fullchain.pem;
    ssl_private_key /etc/letsencrypt/live/$FULL_DOMAIN/privkey.pem;
    
    # SSL 安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;

    # 反向代理到 Ghost
    location / {
        proxy_pass http://ghost:2368;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_set_header X-Forwarded-Host \$host;
        proxy_set_header X-Forwarded-Port \$server_port;
        
        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 缓存配置
        proxy_cache_bypass \$http_upgrade;
        proxy_buffering off;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://ghost:2368;
        proxy_set_header Host \$host;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF
    
    log_info "Nginx SSL 配置创建完成"
}

# 上传文件到 VPS
upload_files_to_vps() {
    log_info "上传文件到 VPS..."
    
    # 在 VPS 上创建目录
    ssh root@$VPS_IP "mkdir -p /opt/ghost-blog"
    
    # 上传项目文件
    scp -r ./* root@$VPS_IP:/opt/ghost-blog/
    
    # 设置权限
    ssh root@$VPS_IP "chmod +x /opt/ghost-blog/scripts/*.sh"
    
    log_info "文件上传完成"
}

# 在 VPS 上获取 SSL 证书
setup_ssl_certificate() {
    log_info "设置 SSL 证书..."
    
    ssh root@$VPS_IP << EOF
        cd /opt/ghost-blog
        
        # 安装 Certbot
        apt install certbot -y
        
        # 获取 SSL 证书
        certbot certonly --standalone -d $FULL_DOMAIN --email admin@$DOMAIN --agree-tos --non-interactive
        
        # 设置自动续期
        echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
        
        echo "SSL 证书设置完成"
EOF
    
    log_info "SSL 证书设置完成"
}

# 启动服务
start_services() {
    log_info "启动 Ghost 博客服务..."
    
    ssh root@$VPS_IP << EOF
        cd /opt/ghost-blog
        
        # 启动服务
        docker-compose -f docker-compose.prod.yml --env-file .env.production up -d
        
        # 等待服务启动
        sleep 30
        
        # 检查服务状态
        docker-compose -f docker-compose.prod.yml ps
        
        echo "服务启动完成"
EOF
    
    log_info "Ghost 博客服务启动完成"
}

# 配置防火墙
setup_firewall() {
    log_info "配置防火墙..."
    
    ssh root@$VPS_IP << 'EOF'
        # 安装并配置 UFW
        apt install ufw -y
        ufw --force reset
        ufw default deny incoming
        ufw default allow outgoing
        ufw allow ssh
        ufw allow 80/tcp
        ufw allow 443/tcp
        ufw --force enable
        
        echo "防火墙配置完成"
EOF
    
    log_info "防火墙配置完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 检查域名解析
    if ! nslookup $FULL_DOMAIN > /dev/null 2>&1; then
        log_warn "域名 $FULL_DOMAIN 解析失败，请检查 DNS 设置"
        log_info "请在域名管理面板添加 A 记录:"
        log_info "类型: A"
        log_info "名称: $SUBDOMAIN"
        log_info "值: $VPS_IP"
    fi
    
    # 检查 HTTP 访问
    if curl -f http://$FULL_DOMAIN > /dev/null 2>&1; then
        log_info "HTTP 访问正常"
    else
        log_warn "HTTP 访问失败"
    fi
    
    # 检查 HTTPS 访问
    if curl -f https://$FULL_DOMAIN > /dev/null 2>&1; then
        log_info "HTTPS 访问正常"
    else
        log_warn "HTTPS 访问失败，可能需要等待 SSL 证书生效"
    fi
    
    log_info "部署验证完成"
}

# 主函数
main() {
    log_info "=== Ghost 博客 VPS 部署开始 ==="
    
    check_local_requirements
    check_vps_connection
    install_docker_on_vps
    create_production_config
    create_nginx_ssl_config
    upload_files_to_vps
    setup_ssl_certificate
    start_services
    setup_firewall
    verify_deployment
    
    log_info "=== 部署完成 ==="
    log_info "博客地址: https://$FULL_DOMAIN"
    log_info "管理后台: https://$FULL_DOMAIN/ghost/"
    log_info ""
    log_info "下一步:"
    log_info "1. 确保域名 DNS 解析正确指向 $VPS_IP"
    log_info "2. 访问管理后台完成初始设置"
    log_info "3. 配置中文内容和主题"
    log_info ""
    log_info "管理命令:"
    log_info "ssh root@$VPS_IP 'cd /opt/ghost-blog && docker-compose -f docker-compose.prod.yml logs -f'"
}

# 执行主函数
main "$@"

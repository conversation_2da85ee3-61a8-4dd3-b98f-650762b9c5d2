# Ghost 博客中文化配置指南

本文档详细说明如何将 Ghost 博客配置为中文界面和内容。

## 概述

Ghost 支持多种语言，包括简体中文。可以通过以下几种方式配置中文：

1. 管理后台设置（推荐）
2. 配置文件修改
3. 环境变量设置
4. 主题级别的中文化

## 方法一：管理后台设置（推荐）

### 步骤 1：访问管理后台
```
http://localhost/ghost
```

### 步骤 2：进入设置页面
1. 登录管理后台
2. 点击左侧菜单的 "Settings"（设置）
3. 选择 "General"（常规设置）

### 步骤 3：修改语言设置
1. 找到 "Publication language" 字段
2. 将值改为以下之一：
   - `zh` - 简体中文
   - `zh-CN` - 中国大陆简体中文
   - `zh-TW` - 繁体中文（台湾）
   - `zh-HK` - 繁体中文（香港）

3. 点击 "Save settings" 保存设置

### 步骤 4：验证设置
- 刷新博客首页，检查语言是否已更改
- 日期格式、分页文本等应显示为中文

## 方法二：配置文件修改

### 修改 Ghost 配置文件

编辑 `ghost/config.production.json` 文件：

```json
{
  "url": "http://localhost",
  "server": {
    "port": 2368,
    "host": "0.0.0.0"
  },
  "i18n": {
    "defaultLocale": "zh"
  },
  "database": {
    "client": "mysql",
    "connection": {
      "host": "mysql",
      "port": 3306,
      "user": "ghost",
      "password": "ghost_password",
      "database": "ghost_production"
    }
  }
}
```

### 重启服务
```bash
docker-compose restart ghost
```

## 方法三：环境变量设置

### 修改 .env 文件

在 `.env` 文件中添加：

```env
# Ghost 语言设置
GHOST_LOCALE=zh
```

### 更新 Docker Compose 配置

确保 `docker-compose.yml` 中的 Ghost 服务包含环境变量：

```yaml
ghost:
  image: ghost:5-alpine
  environment:
    - GHOST_LOCALE=${GHOST_LOCALE:-zh}
```

## 方法四：主题级别中文化

### 下载中文主题

1. **官方主题中文化**
   - 大多数官方主题已支持中文
   - 通过语言设置自动切换

2. **第三方中文主题**
   - 搜索专门为中文优化的主题
   - 下载并上传到 Ghost

### 自定义主题中文化

如果使用自定义主题，需要修改主题文件：

1. **编辑主题的 locales 文件**
   ```
   content/themes/your-theme/locales/zh.json
   ```

2. **添加中文翻译**
   ```json
   {
     "Back": "返回",
     "Newer Posts": "较新文章",
     "Older Posts": "较旧文章",
     "Page {page} of {pages}": "第 {page} 页，共 {pages} 页",
     "Subscribe": "订阅",
     "Website": "网站",
     "Location": "位置",
     "1 post": "1 篇文章",
     "{count} posts": "{count} 篇文章",
     "1 min read": "1 分钟阅读",
     "{count} min read": "{count} 分钟阅读"
   }
   ```

## 中文化验证

### 检查项目

1. **前台界面**
   - 导航菜单
   - 分页文本
   - 日期格式
   - 阅读时间
   - 搜索提示

2. **管理后台**
   - 菜单项
   - 按钮文本
   - 表单标签
   - 错误消息

3. **邮件模板**
   - 订阅确认邮件
   - 密码重置邮件
   - 通知邮件

### 测试命令

```bash
# 检查当前语言设置
curl -s http://localhost/ | grep -i "lang="

# 检查管理后台语言
curl -s http://localhost/ghost/ | grep -i "locale"
```

## 常见问题

### 问题 1：设置后语言没有改变

**解决方案：**
1. 清除浏览器缓存
2. 重启 Ghost 服务
3. 检查主题是否支持中文

```bash
# 重启 Ghost 服务
docker-compose restart ghost

# 清除 Ghost 缓存
docker-compose exec ghost ghost cache clear
```

### 问题 2：部分内容仍显示英文

**原因：**
- 主题不支持中文
- 自定义内容需要手动翻译

**解决方案：**
1. 更换支持中文的主题
2. 手动翻译自定义内容
3. 修改主题的语言文件

### 问题 3：日期格式不正确

**解决方案：**
在 Ghost 配置中添加时区设置：

```json
{
  "i18n": {
    "defaultLocale": "zh",
    "timezone": "Asia/Shanghai"
  }
}
```

## 高级配置

### 多语言支持

如果需要支持多种语言：

1. **安装多语言插件**
2. **配置语言切换器**
3. **创建多语言内容**

### SEO 优化

为中文内容优化 SEO：

1. **设置中文 meta 标签**
   ```html
   <meta name="language" content="zh-CN">
   <meta http-equiv="content-language" content="zh-CN">
   ```

2. **配置中文 URL 结构**
   ```json
   {
     "routes": {
       "/": {
         "template": "index",
         "data": "page.home"
       },
       "/关于/": {
         "template": "page",
         "data": "page.about"
       }
     }
   }
   ```

## 维护和更新

### 定期检查

1. **Ghost 版本更新**
   - 检查新版本的中文支持
   - 更新语言文件

2. **主题更新**
   - 保持主题最新版本
   - 检查中文翻译完整性

### 备份语言设置

```bash
# 备份 Ghost 配置
docker-compose exec ghost cp /var/lib/ghost/config.production.json /var/lib/ghost/content/config-backup.json

# 备份主题语言文件
docker run --rm -v ghost_content:/data -v $(pwd):/backup alpine tar czf /backup/theme-locales-backup.tar.gz -C /data/themes .
```

## 总结

通过以上方法，您可以成功将 Ghost 博客配置为中文界面。推荐使用管理后台设置的方法，因为它最简单且不需要重启服务。

如果遇到问题，请检查：
1. Ghost 版本是否支持中文
2. 主题是否兼容中文
3. 浏览器缓存是否已清除
4. 服务是否已重启

---

**注意**: 某些第三方主题可能需要额外的中文化配置。建议选择官方主题或明确支持中文的主题。

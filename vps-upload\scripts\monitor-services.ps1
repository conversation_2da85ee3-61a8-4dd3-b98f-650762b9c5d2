# 服务监控和自动重启脚本 (PowerShell 版本)
# 监控 Ghost 博客服务并在需要时自动重启

param(
    [int]$CheckInterval = 60,  # 检查间隔（秒）
    [int]$MaxRetries = 3,      # 最大重试次数
    [switch]$DryRun,           # 仅检查不执行重启
    [switch]$Verbose
)

# 设置错误处理
$ErrorActionPreference = "Continue"

# 全局变量
$script:FailureCount = @{}
$script:LastRestartTime = @{}

# 日志函数
function Write-Info {
    param($Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [INFO] $Message" -ForegroundColor Green
}

function Write-Warn {
    param($Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [ERROR] $Message" -ForegroundColor Red
}

function Write-Debug {
    param($Message)
    if ($Verbose) {
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        Write-Host "[$timestamp] [DEBUG] $Message" -ForegroundColor Blue
    }
}

# 检查容器状态
function Test-ContainerHealth {
    param(
        [string]$ContainerName,
        [string]$ServiceName
    )
    
    try {
        # 检查容器是否运行
        $containerStatus = docker inspect --format='{{.State.Status}}' $ContainerName 2>$null
        if ($LASTEXITCODE -ne 0) {
            Write-Error "$ServiceName 容器不存在"
            return $false
        }
        
        if ($containerStatus -ne "running") {
            Write-Error "$ServiceName 容器状态: $containerStatus"
            return $false
        }
        
        # 检查健康状态
        $healthStatus = docker inspect --format='{{.State.Health.Status}}' $ContainerName 2>$null
        if ($LASTEXITCODE -eq 0 -and $healthStatus) {
            if ($healthStatus -eq "healthy") {
                Write-Debug "$ServiceName 健康状态: $healthStatus"
                return $true
            } elseif ($healthStatus -eq "starting") {
                Write-Warn "$ServiceName 正在启动中..."
                return $true  # 启动中也认为是正常的
            } else {
                Write-Error "$ServiceName 健康状态: $healthStatus"
                return $false
            }
        } else {
            # 没有健康检查配置，只检查运行状态
            Write-Debug "$ServiceName 没有健康检查配置，仅检查运行状态"
            return $true
        }
    } catch {
        Write-Error "检查 $ServiceName 容器健康状态时发生错误: $($_.Exception.Message)"
        return $false
    }
}

# 重启服务
function Restart-Service {
    param(
        [string]$ServiceName,
        [string]$ContainerName
    )
    
    Write-Warn "准备重启 $ServiceName 服务..."
    
    if ($DryRun) {
        Write-Info "[DRY RUN] 将重启 $ServiceName 服务"
        return $true
    }
    
    try {
        # 记录重启时间
        $script:LastRestartTime[$ServiceName] = Get-Date
        
        # 重启服务
        Write-Info "重启 $ServiceName 服务..."
        docker-compose restart $ServiceName.ToLower()
        
        if ($LASTEXITCODE -eq 0) {
            Write-Info "$ServiceName 服务重启成功"
            
            # 等待服务启动
            Write-Info "等待 $ServiceName 服务启动..."
            Start-Sleep -Seconds 30
            
            # 重置失败计数
            $script:FailureCount[$ServiceName] = 0
            
            return $true
        } else {
            Write-Error "$ServiceName 服务重启失败"
            return $false
        }
    } catch {
        Write-Error "重启 $ServiceName 服务时发生错误: $($_.Exception.Message)"
        return $false
    }
}

# 处理服务故障
function Handle-ServiceFailure {
    param(
        [string]$ServiceName,
        [string]$ContainerName
    )
    
    # 增加失败计数
    if (-not $script:FailureCount.ContainsKey($ServiceName)) {
        $script:FailureCount[$ServiceName] = 0
    }
    $script:FailureCount[$ServiceName]++
    
    Write-Warn "$ServiceName 服务检查失败 (第 $($script:FailureCount[$ServiceName]) 次)"
    
    # 检查是否需要重启
    if ($script:FailureCount[$ServiceName] -ge $MaxRetries) {
        # 检查最近是否已经重启过
        $lastRestart = $script:LastRestartTime[$ServiceName]
        if ($lastRestart -and ((Get-Date) - $lastRestart).TotalMinutes -lt 10) {
            Write-Error "$ServiceName 服务在 10 分钟内已重启过，跳过重启"
            return
        }
        
        # 尝试重启服务
        if (Restart-Service -ServiceName $ServiceName -ContainerName $ContainerName) {
            Write-Info "$ServiceName 服务重启成功"
        } else {
            Write-Error "$ServiceName 服务重启失败，需要手动干预"
        }
    }
}

# 监控单个服务
function Monitor-Service {
    param(
        [string]$ServiceName,
        [string]$ContainerName
    )
    
    Write-Debug "检查 $ServiceName 服务..."
    
    if (Test-ContainerHealth -ContainerName $ContainerName -ServiceName $ServiceName) {
        # 服务正常，重置失败计数
        if ($script:FailureCount.ContainsKey($ServiceName)) {
            if ($script:FailureCount[$ServiceName] -gt 0) {
                Write-Info "$ServiceName 服务恢复正常"
                $script:FailureCount[$ServiceName] = 0
            }
        }
    } else {
        # 服务异常，处理故障
        Handle-ServiceFailure -ServiceName $ServiceName -ContainerName $ContainerName
    }
}

# 主监控循环
function Start-Monitoring {
    Write-Info "开始监控 Ghost 博客服务..."
    Write-Info "检查间隔: $CheckInterval 秒"
    Write-Info "最大重试次数: $MaxRetries"
    if ($DryRun) {
        Write-Info "运行模式: 仅检查 (不执行重启)"
    }
    
    # 服务列表
    $services = @(
        @{ Name = "MySQL"; Container = "ghost_mysql" },
        @{ Name = "Ghost"; Container = "ghost_app" },
        @{ Name = "Nginx"; Container = "ghost_nginx" }
    )
    
    try {
        while ($true) {
            Write-Debug "开始新一轮服务检查..."
            
            foreach ($service in $services) {
                Monitor-Service -ServiceName $service.Name -ContainerName $service.Container
            }
            
            Write-Debug "本轮检查完成，等待 $CheckInterval 秒..."
            Start-Sleep -Seconds $CheckInterval
        }
    } catch {
        Write-Error "监控过程中发生错误: $($_.Exception.Message)"
        throw
    }
}

# 信号处理
function Stop-Monitoring {
    Write-Info "收到停止信号，正在退出监控..."
    exit 0
}

# 注册信号处理
Register-EngineEvent -SourceIdentifier PowerShell.Exiting -Action { Stop-Monitoring }

# 主函数
function Main {
    try {
        # 检查 Docker 和 Docker Compose
        if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
            Write-Error "Docker 未安装或不在 PATH 中"
            exit 1
        }
        
        if (-not (Get-Command docker-compose -ErrorAction SilentlyContinue)) {
            Write-Error "Docker Compose 未安装或不在 PATH 中"
            exit 1
        }
        
        # 开始监控
        Start-Monitoring
    } catch {
        Write-Error "监控启动失败: $($_.Exception.Message)"
        exit 1
    }
}

# 执行主函数
Main

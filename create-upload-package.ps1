# 创建 VPS 上传包脚本

param(
    [string]$OutputPath = "ghost-blog-vps.zip"
)

Write-Host "=== 创建 Ghost 博客 VPS 上传包 ===" -ForegroundColor Green

# 检查 vps-upload 文件夹是否存在
if (-not (Test-Path "vps-upload")) {
    Write-Host "错误: vps-upload 文件夹不存在" -ForegroundColor Red
    exit 1
}

# 显示文件清单
Write-Host "`n📁 包含的文件:" -ForegroundColor Yellow
Get-ChildItem "vps-upload" -Recurse | ForEach-Object {
    $relativePath = $_.FullName.Replace((Get-Location).Path + "\vps-upload\", "")
    if ($_.PSIsContainer) {
        Write-Host "  📁 $relativePath/" -ForegroundColor Blue
    } else {
        $size = [math]::Round($_.Length / 1KB, 1)
        Write-Host "  📄 $relativePath ($size KB)" -ForegroundColor Gray
    }
}

# 创建压缩包
Write-Host "`n📦 创建压缩包..." -ForegroundColor Yellow
try {
    if (Test-Path $OutputPath) {
        Remove-Item $OutputPath -Force
    }
    
    Compress-Archive -Path "vps-upload\*" -DestinationPath $OutputPath -Force
    
    $zipSize = [math]::Round((Get-Item $OutputPath).Length / 1MB, 2)
    Write-Host "✅ 压缩包创建成功: $OutputPath ($zipSize MB)" -ForegroundColor Green
    
} catch {
    Write-Host "❌ 创建压缩包失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 显示上传说明
Write-Host "`n🚀 上传说明:" -ForegroundColor Cyan
Write-Host "1. 上传压缩包到 VPS:" -ForegroundColor White
Write-Host "   scp $OutputPath root@your-vps-ip:/tmp/" -ForegroundColor Gray

Write-Host "`n2. 在 VPS 上解压:" -ForegroundColor White
Write-Host "   ssh root@your-vps-ip" -ForegroundColor Gray
Write-Host "   cd /opt" -ForegroundColor Gray
Write-Host "   unzip /tmp/$OutputPath" -ForegroundColor Gray
Write-Host "   mv vps-upload ghost-blog-xing2006" -ForegroundColor Gray

Write-Host "`n3. 设置权限:" -ForegroundColor White
Write-Host "   chmod +x /opt/ghost-blog-xing2006/scripts/*.sh" -ForegroundColor Gray

Write-Host "`n4. 执行部署:" -ForegroundColor White
Write-Host "   cd /opt/ghost-blog-xing2006" -ForegroundColor Gray
Write-Host "   ./scripts/deploy-multi-site.sh xing2006.me your-vps-ip" -ForegroundColor Gray

Write-Host "`n📚 详细说明请查看压缩包内的 README.md 文件" -ForegroundColor Yellow

Write-Host "`n🎉 准备完成！现在可以上传到 VPS 了。" -ForegroundColor Green

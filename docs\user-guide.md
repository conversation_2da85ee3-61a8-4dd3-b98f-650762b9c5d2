# Ghost 博客用户使用指南

## 目录
1. [初始设置](#初始设置)
2. [管理后台介绍](#管理后台介绍)
3. [创建和管理内容](#创建和管理内容)
4. [主题和设计](#主题和设计)
5. [用户和权限管理](#用户和权限管理)
6. [SEO 和营销](#seo-和营销)
7. [高级功能](#高级功能)

## 初始设置

### 首次登录
1. 访问管理后台: `http://localhost/ghost`
2. 使用环境变量中配置的管理员邮箱和密码登录
3. 如果是首次部署，系统会引导您完成初始设置

### 基本配置
1. **站点信息设置**
   - 进入 `Settings > General`
   - 设置站点标题和描述
   - 上传站点图标和封面图片
   - 配置时区和语言

2. **用户资料设置**
   - 点击右上角头像 > `Your Profile`
   - 完善个人信息和头像
   - 设置作者简介

## 管理后台介绍

### 主要功能区域

#### 1. 仪表板 (Dashboard)
- 显示站点统计信息
- 最近发布的文章
- 访问量和订阅者数据

#### 2. 内容管理 (Posts)
- 文章列表和管理
- 草稿和已发布文章
- 文章搜索和筛选

#### 3. 页面管理 (Pages)
- 静态页面创建和管理
- 关于页面、联系页面等

#### 4. 标签管理 (Tags)
- 创建和管理文章标签
- 标签描述和元数据设置

#### 5. 会员管理 (Members)
- 订阅者管理
- 会员导入和导出
- 邮件列表管理

#### 6. 设置 (Settings)
- 站点基本设置
- 主题和设计
- 集成和高级设置

## 创建和管理内容

### 创建新文章

1. **开始写作**
   - 点击 `New Post` 或使用快捷键 `Ctrl+Alt+N`
   - 输入文章标题
   - 开始在编辑器中写作

2. **使用编辑器**
   - **文本格式**: 支持 Markdown 和富文本编辑
   - **插入图片**: 拖拽图片到编辑器或点击 `+` 按钮
   - **添加卡片**: 支持图片、视频、代码块、引用等
   - **分隔符**: 使用 `---` 创建分隔线

3. **文章设置**
   - 点击右侧设置图标
   - 设置文章 URL、发布时间
   - 添加标签和摘要
   - 设置特色图片

### 文章状态管理

- **草稿 (Draft)**: 未发布的文章
- **已发布 (Published)**: 公开可见的文章
- **计划发布 (Scheduled)**: 定时发布的文章

### 内容组织

#### 标签系统
1. **创建标签**
   - 进入 `Tags` 页面
   - 点击 `New Tag`
   - 设置标签名称、颜色和描述

2. **使用标签**
   - 在文章设置中添加标签
   - 支持多个标签
   - 标签可用于内容分类和导航

#### 特色内容
- 将重要文章设置为 "Featured"
- 特色文章会在首页突出显示
- 可用于推广重要内容

## 主题和设计

### 主题管理

1. **安装主题**
   - 进入 `Settings > Design`
   - 点击 `Change Theme`
   - 上传主题文件或选择默认主题

2. **自定义主题**
   - 修改主题颜色和字体
   - 自定义导航菜单
   - 设置社交媒体链接

### 导航设置
1. **主导航**
   - 进入 `Settings > Design > Navigation`
   - 添加菜单项和链接
   - 支持外部链接和内部页面

2. **次级导航**
   - 设置页脚链接
   - 社交媒体图标
   - 版权信息

## 用户和权限管理

### 用户角色

1. **Owner (所有者)**
   - 完全管理权限
   - 可以删除站点
   - 管理所有用户

2. **Administrator (管理员)**
   - 几乎所有权限
   - 不能删除所有者
   - 可以管理其他用户

3. **Editor (编辑)**
   - 可以发布和编辑所有内容
   - 不能管理用户和设置

4. **Author (作者)**
   - 只能管理自己的内容
   - 可以发布文章

5. **Contributor (贡献者)**
   - 只能创建草稿
   - 不能发布内容

### 邀请用户
1. 进入 `Settings > Staff`
2. 点击 `Invite People`
3. 输入邮箱地址和选择角色
4. 发送邀请邮件

## SEO 和营销

### SEO 优化

1. **文章 SEO**
   - 设置 SEO 标题和描述
   - 优化 URL 结构
   - 使用合适的标签

2. **站点 SEO**
   - 进入 `Settings > General`
   - 设置站点元描述
   - 配置社交媒体卡片

### 社交媒体集成
1. **Facebook 卡片**
   - 设置 Facebook 标题和描述
   - 上传 Facebook 图片

2. **Twitter 卡片**
   - 配置 Twitter 标题和描述
   - 设置 Twitter 图片

### 邮件订阅
1. **启用会员功能**
   - 进入 `Settings > Members`
   - 启用会员注册
   - 配置邮件设置

2. **邮件模板**
   - 自定义欢迎邮件
   - 设置订阅确认邮件

## 高级功能

### 代码注入
1. **站点级代码注入**
   - 进入 `Settings > Code Injection`
   - 添加 Google Analytics
   - 自定义 CSS 和 JavaScript

2. **文章级代码注入**
   - 在文章设置中添加自定义代码
   - 支持 HTML、CSS 和 JavaScript

### 集成功能

#### Google Analytics
```html
<!-- 在 Site Header 中添加 -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_TRACKING_ID');
</script>
```

#### Disqus 评论
```html
<!-- 在 Post Footer 中添加 -->
<div id="disqus_thread"></div>
<script>
var disqus_config = function () {
    this.page.url = '{{url absolute="true"}}';
    this.page.identifier = '{{id}}';
};
(function() {
    var d = document, s = d.createElement('script');
    s.src = 'https://YOUR_DISQUS_SHORTNAME.disqus.com/embed.js';
    s.setAttribute('data-timestamp', +new Date());
    (d.head || d.body).appendChild(s);
})();
</script>
```

### 备份和导出
1. **导出内容**
   - 进入 `Settings > Labs`
   - 点击 `Export your content`
   - 下载 JSON 格式的备份文件

2. **导入内容**
   - 在同一页面选择导入文件
   - 支持从其他 Ghost 站点导入
   - 支持从 WordPress 导入

## 常用快捷键

- `Ctrl+Alt+N`: 新建文章
- `Ctrl+S`: 保存草稿
- `Ctrl+Alt+P`: 发布文章
- `Ctrl+/`: 显示快捷键帮助
- `Ctrl+Enter`: 在编辑器中插入新段落

## 移动端管理

Ghost 管理后台完全支持移动设备：
- 响应式设计适配手机和平板
- 支持触摸操作
- 可以在移动设备上编辑和发布内容

## 最佳实践

1. **内容策略**
   - 定期发布高质量内容
   - 使用一致的标签系统
   - 优化图片大小和质量

2. **SEO 优化**
   - 使用描述性的 URL
   - 编写吸引人的标题和摘要
   - 合理使用标签和分类

3. **用户体验**
   - 保持导航简洁明了
   - 确保网站加载速度
   - 提供搜索功能

4. **安全性**
   - 定期更新密码
   - 限制管理员账户数量
   - 定期备份内容

---

**版本**: 1.0.0  
**最后更新**: 2025-07-26  
**维护者**: Ghost Blog Team

# Ghost 博客站点配置

# 上游服务器定义
upstream ghost_backend {
    server ghost:2368;
    keepalive 32;
}

# HTTP 服务器配置
server {
    listen 80;
    server_name localhost;
    
    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # 根目录代理到 Ghost
    location / {
        proxy_pass http://ghost_backend;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 连接设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
        
        # HTTP 版本和连接复用
        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }

    # 静态文件缓存 - Ghost 内容文件
    location ~* ^/content/images/.*\.(jpg|jpeg|png|gif|ico|svg|webp)$ {
        alias /var/lib/ghost/content/images/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        
        # 图片压缩
        gzip_static on;
        
        # 错误处理
        try_files $uri @ghost;
    }

    # CSS 和 JS 文件缓存
    location ~* \.(css|js)$ {
        proxy_pass http://ghost_backend;
        proxy_set_header Host $http_host;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
    }

    # 字体文件缓存
    location ~* \.(woff|woff2|ttf|eot)$ {
        proxy_pass http://ghost_backend;
        proxy_set_header Host $http_host;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }

    # Ghost 管理后台
    location ^~ /ghost/ {
        proxy_pass http://ghost_backend;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 管理后台需要更长的超时时间
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 禁用缓存
        expires off;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }

    # API 路由
    location ^~ /ghost/api/ {
        proxy_pass http://ghost_backend;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # API 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # 健康检查端点
    location /nginx-health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    # 回退到 Ghost 处理
    location @ghost {
        proxy_pass http://ghost_backend;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

version: '3.8'

services:
  # MySQL 数据库服务
  mysql:
    image: mysql:8.0
    container_name: ghost_mysql_prod
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data_prod:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d:ro
    networks:
      - ghost_network_prod
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
      interval: 30s
      start_period: 60s
    command: --default-authentication-plugin=mysql_native_password
    security_opt:
      - no-new-privileges:true

  # Ghost 博客应用
  ghost:
    image: ghost:5-alpine
    container_name: ghost_app_prod
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      # 数据库配置
      database__client: mysql
      database__connection__host: mysql
      database__connection__user: ${MYSQL_USER}
      database__connection__password: ${MYSQL_PASSWORD}
      database__connection__database: ${MYSQL_DATABASE}
      # Ghost 配置
      url: ${GHOST_URL}
      NODE_ENV: production
    volumes:
      - ghost_content_prod:/var/lib/ghost/content
      - ./ghost/config.production.json:/var/lib/ghost/config.production.json:ro
    networks:
      - ghost_network_prod
    healthcheck:
      test: ["CMD", "sh", "-c", "wget --quiet --tries=1 --spider http://localhost:2368/ || exit 1"]
      timeout: 30s
      retries: 3
      interval: 60s
      start_period: 120s
    security_opt:
      - no-new-privileges:true
    user: "1000:1000"

  # Nginx 反向代理服务
  nginx:
    image: nginx:alpine
    container_name: ghost_nginx_prod
    restart: unless-stopped
    depends_on:
      - ghost
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - /etc/letsencrypt:/etc/letsencrypt:ro
      - /var/www/certbot:/var/www/certbot:ro
      - nginx_logs_prod:/var/log/nginx
    networks:
      - ghost_network_prod
    healthcheck:
      test: ["CMD", "sh", "-c", "nginx -t && wget --quiet --tries=1 --spider http://localhost/nginx-health || exit 1"]
      timeout: 15s
      retries: 3
      interval: 30s
      start_period: 30s
    security_opt:
      - no-new-privileges:true

  # Certbot SSL 证书管理
  certbot:
    image: certbot/certbot
    container_name: ghost_certbot_prod
    restart: "no"
    volumes:
      - /etc/letsencrypt:/etc/letsencrypt
      - /var/www/certbot:/var/www/certbot
    command: certonly --webroot --webroot-path=/var/www/certbot --email ${SSL_EMAIL} --agree-tos --no-eff-email -d ${DOMAIN_NAME}
    depends_on:
      - nginx

# 数据卷
volumes:
  ghost_content_prod:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/ghost-blog/data/ghost-content
  mysql_data_prod:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/ghost-blog/data/mysql-data
  nginx_logs_prod:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/ghost-blog/logs/nginx

# 网络
networks:
  ghost_network_prod:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Ghost 博客中文内容设置指导脚本

Write-Host "=== Ghost 博客中文化设置指导 ===" -ForegroundColor Green

Write-Host "`n📋 当前中文化状态：" -ForegroundColor Yellow
Write-Host "✅ HTML 语言标签已设置为 zh-CN" -ForegroundColor Green
Write-Host "✅ 配置文件语言设置正确" -ForegroundColor Green
Write-Host "⚠️  界面文本需要手动中文化" -ForegroundColor Yellow

Write-Host "`n🔧 完成中文化的步骤：" -ForegroundColor Cyan

Write-Host "`n1. 设置网站基本信息（必须）：" -ForegroundColor White
Write-Host "   - 访问：http://localhost/ghost/" -ForegroundColor Gray
Write-Host "   - 进入：Settings → General" -ForegroundColor Gray
Write-Host "   - 修改：Title & description" -ForegroundColor Gray
Write-Host "     * Site title: 我的博客" -ForegroundColor Gray
Write-Host "     * Site description: 这是我的个人博客" -ForegroundColor Gray

Write-Host "`n2. 设置导航菜单：" -ForegroundColor White
Write-Host "   - 进入：Settings → Design → Navigation" -ForegroundColor Gray
Write-Host "   - 修改导航项：" -ForegroundColor Gray
Write-Host "     * Home → 首页" -ForegroundColor Gray
Write-Host "     * About → 关于" -ForegroundColor Gray
Write-Host "     * Contact → 联系" -ForegroundColor Gray

Write-Host "`n3. 创建中文页面：" -ForegroundColor White
Write-Host "   - 进入：Pages → New page" -ForegroundColor Gray
Write-Host "   - 创建页面：" -ForegroundColor Gray
Write-Host "     * 关于我们" -ForegroundColor Gray
Write-Host "     * 联系方式" -ForegroundColor Gray
Write-Host "     * 隐私政策" -ForegroundColor Gray

Write-Host "`n4. 发布中文文章：" -ForegroundColor White
Write-Host "   - 进入：Posts → New post" -ForegroundColor Gray
Write-Host "   - 发布中文内容替换默认的英文文章" -ForegroundColor Gray

Write-Host "`n5. 设置中文标签：" -ForegroundColor White
Write-Host "   - 进入：Tags → New tag" -ForegroundColor Gray
Write-Host "   - 创建中文标签：" -ForegroundColor Gray
Write-Host "     * 技术分享" -ForegroundColor Gray
Write-Host "     * 生活随笔" -ForegroundColor Gray
Write-Host "     * 学习笔记" -ForegroundColor Gray

Write-Host "`n📝 中文化模板内容：" -ForegroundColor Cyan

Write-Host "`n网站标题建议：" -ForegroundColor White
$titles = @(
    "我的个人博客",
    "技术分享空间", 
    "学习成长记录",
    "思考与分享",
    "代码人生"
)
$titles | ForEach-Object { Write-Host "   - $_" -ForegroundColor Gray }

Write-Host "`n网站描述建议：" -ForegroundColor White
$descriptions = @(
    "记录技术学习和生活感悟的个人空间",
    "分享编程技术、学习心得和生活思考",
    "一个专注于技术分享和个人成长的博客",
    "记录代码人生，分享技术心得",
    "用文字记录成长，用代码改变世界"
)
$descriptions | ForEach-Object { Write-Host "   - $_" -ForegroundColor Gray }

Write-Host "`n📄 示例文章标题：" -ForegroundColor White
$postTitles = @(
    "欢迎来到我的博客",
    "Docker 部署 Ghost 博客完整指南", 
    "我的编程学习之路",
    "技术博客写作心得分享",
    "开源项目贡献指南"
)
$postTitles | ForEach-Object { Write-Host "   - $_" -ForegroundColor Gray }

Write-Host "`n🎨 主题中文化（高级）：" -ForegroundColor Cyan
Write-Host "如果需要更深度的中文化，可以：" -ForegroundColor White
Write-Host "1. 下载支持中文的第三方主题" -ForegroundColor Gray
Write-Host "2. 修改当前主题的模板文件" -ForegroundColor Gray
Write-Host "3. 使用 Ghost 的国际化功能" -ForegroundColor Gray

Write-Host "`n🔗 快速链接：" -ForegroundColor Cyan
Write-Host "- 博客首页：http://localhost" -ForegroundColor Gray
Write-Host "- 管理后台：http://localhost/ghost/" -ForegroundColor Gray
Write-Host "- 中文化文档：docs/chinese-localization.md" -ForegroundColor Gray

Write-Host "`n💡 提示：" -ForegroundColor Yellow
Write-Host "Ghost 的 'Publication language' 设置主要影响：" -ForegroundColor White
Write-Host "- HTML lang 属性（SEO 优化）" -ForegroundColor Gray
Write-Host "- 日期格式显示" -ForegroundColor Gray  
Write-Host "- 搜索引擎识别" -ForegroundColor Gray
Write-Host "- 部分主题的本地化文本" -ForegroundColor Gray
Write-Host "`n界面文本的中文化需要通过内容管理来实现。" -ForegroundColor White

Write-Host "`n🎉 开始您的中文博客之旅吧！" -ForegroundColor Green

# Ghost 博客集成测试脚本
# 测试容器启动、连接和基本功能

param(
    [switch]$Verbose,
    [switch]$SkipCleanup,
    [int]$Timeout = 300  # 5分钟超时
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 测试结果统计
$script:TestResults = @{
    Total = 0
    Passed = 0
    Failed = 0
    Skipped = 0
}

# 日志函数
function Write-TestInfo {
    param($Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [TEST] $Message" -ForegroundColor Cyan
}

function Write-TestPass {
    param($Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [PASS] $Message" -ForegroundColor Green
    $script:TestResults.Passed++
}

function Write-TestFail {
    param($Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [FAIL] $Message" -ForegroundColor Red
    $script:TestResults.Failed++
}

function Write-TestSkip {
    param($Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [SKIP] $Message" -ForegroundColor Yellow
    $script:TestResults.Skipped++
}

function Write-TestDebug {
    param($Message)
    if ($Verbose) {
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        Write-Host "[$timestamp] [DEBUG] $Message" -ForegroundColor Blue
    }
}

# 测试函数
function Test-DockerAvailable {
    Write-TestInfo "测试 Docker 可用性..."
    $script:TestResults.Total++
    
    try {
        $version = docker --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-TestPass "Docker 可用: $version"
            return $true
        } else {
            Write-TestFail "Docker 不可用"
            return $false
        }
    } catch {
        Write-TestFail "Docker 检查失败: $($_.Exception.Message)"
        return $false
    }
}

function Test-DockerComposeAvailable {
    Write-TestInfo "测试 Docker Compose 可用性..."
    $script:TestResults.Total++
    
    try {
        $version = docker-compose --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-TestPass "Docker Compose 可用: $version"
            return $true
        } else {
            Write-TestFail "Docker Compose 不可用"
            return $false
        }
    } catch {
        Write-TestFail "Docker Compose 检查失败: $($_.Exception.Message)"
        return $false
    }
}

function Test-EnvironmentFile {
    Write-TestInfo "测试环境配置文件..."
    $script:TestResults.Total++
    
    if (Test-Path ".env") {
        Write-TestPass "环境配置文件存在"
        
        # 检查必要的环境变量
        $envContent = Get-Content ".env"
        $requiredVars = @("MYSQL_ROOT_PASSWORD", "MYSQL_DATABASE", "MYSQL_USER", "MYSQL_PASSWORD", "GHOST_URL")
        $missingVars = @()
        
        foreach ($var in $requiredVars) {
            if (-not ($envContent | Where-Object { $_ -match "^$var=" })) {
                $missingVars += $var
            }
        }
        
        if ($missingVars.Count -eq 0) {
            Write-TestPass "所有必要的环境变量都已配置"
            return $true
        } else {
            Write-TestFail "缺少环境变量: $($missingVars -join ', ')"
            return $false
        }
    } else {
        Write-TestFail "环境配置文件 .env 不存在"
        return $false
    }
}

function Test-ContainerStartup {
    Write-TestInfo "测试容器启动..."
    $script:TestResults.Total++
    
    try {
        # 停止现有容器
        Write-TestDebug "停止现有容器..."
        docker-compose down --remove-orphans 2>$null
        
        # 启动容器
        Write-TestDebug "启动容器..."
        $output = docker-compose up -d 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-TestPass "容器启动成功"
            return $true
        } else {
            Write-TestFail "容器启动失败: $output"
            return $false
        }
    } catch {
        Write-TestFail "容器启动测试失败: $($_.Exception.Message)"
        return $false
    }
}

function Test-ContainerHealth {
    Write-TestInfo "测试容器健康状态..."
    $script:TestResults.Total++
    
    $containers = @("ghost_mysql", "ghost_app", "ghost_nginx")
    $maxWait = 120  # 2分钟
    $waited = 0
    
    while ($waited -lt $maxWait) {
        $allHealthy = $true
        
        foreach ($container in $containers) {
            try {
                $status = docker inspect --format='{{.State.Status}}' $container 2>$null
                if ($status -ne "running") {
                    $allHealthy = $false
                    break
                }
            } catch {
                $allHealthy = $false
                break
            }
        }
        
        if ($allHealthy) {
            Write-TestPass "所有容器都在运行"
            return $true
        }
        
        Write-TestDebug "等待容器启动... ($waited/$maxWait 秒)"
        Start-Sleep -Seconds 10
        $waited += 10
    }
    
    Write-TestFail "容器健康检查超时"
    return $false
}

function Test-DatabaseConnection {
    Write-TestInfo "测试数据库连接..."
    $script:TestResults.Total++
    
    try {
        # 等待 MySQL 完全启动
        $maxWait = 60
        $waited = 0
        
        while ($waited -lt $maxWait) {
            $result = docker exec ghost_mysql mysqladmin ping -h localhost --silent 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-TestPass "数据库连接成功"
                return $true
            }
            
            Write-TestDebug "等待数据库启动... ($waited/$maxWait 秒)"
            Start-Sleep -Seconds 5
            $waited += 5
        }
        
        Write-TestFail "数据库连接超时"
        return $false
    } catch {
        Write-TestFail "数据库连接测试失败: $($_.Exception.Message)"
        return $false
    }
}

function Test-GhostApplication {
    Write-TestInfo "测试 Ghost 应用（通过 Nginx）..."
    $script:TestResults.Total++

    try {
        # 等待 Ghost 应用通过 Nginx 启动
        $maxWait = 120
        $waited = 0

        while ($waited -lt $maxWait) {
            try {
                $response = Invoke-WebRequest -Uri "http://localhost/" -UseBasicParsing -TimeoutSec 10
                if ($response.StatusCode -eq 200 -and $response.Content -match "Ghost") {
                    Write-TestPass "Ghost 应用（通过 Nginx）响应正常"
                    return $true
                }
            } catch {
                # 继续等待
            }

            Write-TestDebug "等待 Ghost 应用启动... ($waited/$maxWait 秒)"
            Start-Sleep -Seconds 10
            $waited += 10
        }

        Write-TestFail "Ghost 应用响应超时"
        return $false
    } catch {
        Write-TestFail "Ghost 应用测试失败: $($_.Exception.Message)"
        return $false
    }
}

function Test-NginxProxy {
    Write-TestInfo "测试 Nginx 反向代理..."
    $script:TestResults.Total++
    
    try {
        $maxWait = 60
        $waited = 0
        
        while ($waited -lt $maxWait) {
            try {
                $response = Invoke-WebRequest -Uri "http://localhost/" -UseBasicParsing -TimeoutSec 10
                if ($response.StatusCode -eq 200 -and $response.Headers.Server -contains "nginx") {
                    Write-TestPass "Nginx 反向代理工作正常"
                    return $true
                }
            } catch {
                # 继续等待
            }
            
            Write-TestDebug "等待 Nginx 启动... ($waited/$maxWait 秒)"
            Start-Sleep -Seconds 5
            $waited += 5
        }
        
        Write-TestFail "Nginx 反向代理响应超时"
        return $false
    } catch {
        Write-TestFail "Nginx 反向代理测试失败: $($_.Exception.Message)"
        return $false
    }
}

function Test-GhostAdmin {
    Write-TestInfo "测试 Ghost 管理后台..."
    $script:TestResults.Total++
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost/ghost/" -UseBasicParsing -TimeoutSec 15
        if ($response.StatusCode -eq 200) {
            Write-TestPass "Ghost 管理后台可访问"
            return $true
        } else {
            Write-TestFail "Ghost 管理后台返回状态码: $($response.StatusCode)"
            return $false
        }
    } catch {
        Write-TestFail "Ghost 管理后台测试失败: $($_.Exception.Message)"
        return $false
    }
}

function Test-ContentPersistence {
    Write-TestInfo "测试数据持久化..."
    $script:TestResults.Total++
    
    try {
        # 检查数据卷是否存在
        $volumes = docker volume ls --format "{{.Name}}"
        $requiredVolumes = @("test6_ghost_content", "test6_mysql_data")
        
        $missingVolumes = @()
        foreach ($volume in $requiredVolumes) {
            if ($volumes -notcontains $volume) {
                $missingVolumes += $volume
            }
        }
        
        if ($missingVolumes.Count -eq 0) {
            Write-TestPass "数据卷创建成功"
            return $true
        } else {
            Write-TestFail "缺少数据卷: $($missingVolumes -join ', ')"
            return $false
        }
    } catch {
        Write-TestFail "数据持久化测试失败: $($_.Exception.Message)"
        return $false
    }
}

function Test-Cleanup {
    if ($SkipCleanup) {
        Write-TestInfo "跳过清理步骤"
        return
    }
    
    Write-TestInfo "清理测试环境..."
    
    try {
        docker-compose down --remove-orphans 2>$null
        Write-TestInfo "测试环境清理完成"
    } catch {
        Write-TestInfo "清理过程中出现警告: $($_.Exception.Message)"
    }
}

# 主测试函数
function Start-IntegrationTests {
    Write-TestInfo "开始 Ghost 博客集成测试..."
    Write-TestInfo "测试超时时间: $Timeout 秒"
    
    $startTime = Get-Date
    
    try {
        # 基础环境测试
        if (-not (Test-DockerAvailable)) { return }
        if (-not (Test-DockerComposeAvailable)) { return }
        if (-not (Test-EnvironmentFile)) { return }
        
        # 容器启动测试
        if (-not (Test-ContainerStartup)) { return }
        if (-not (Test-ContainerHealth)) { return }
        
        # 服务连接测试
        if (-not (Test-DatabaseConnection)) { return }
        if (-not (Test-GhostApplication)) { return }
        if (-not (Test-NginxProxy)) { return }
        
        # 功能测试
        if (-not (Test-GhostAdmin)) { return }
        if (-not (Test-ContentPersistence)) { return }
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalSeconds
        
        Write-TestInfo "所有测试完成，耗时: $([math]::Round($duration, 2)) 秒"
        
    } catch {
        Write-TestFail "测试过程中发生错误: $($_.Exception.Message)"
    } finally {
        Test-Cleanup
        
        # 输出测试结果
        Write-Host "`n=== 测试结果汇总 ===" -ForegroundColor White
        Write-Host "总计: $($script:TestResults.Total)" -ForegroundColor White
        Write-Host "通过: $($script:TestResults.Passed)" -ForegroundColor Green
        Write-Host "失败: $($script:TestResults.Failed)" -ForegroundColor Red
        Write-Host "跳过: $($script:TestResults.Skipped)" -ForegroundColor Yellow
        
        if ($script:TestResults.Failed -eq 0) {
            Write-Host "`n✅ 所有测试通过！" -ForegroundColor Green
            exit 0
        } else {
            Write-Host "`n❌ 部分测试失败！" -ForegroundColor Red
            exit 1
        }
    }
}

# 执行测试
Start-IntegrationTests

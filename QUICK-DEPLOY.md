# Ghost 博客 VPS 快速部署指南

## 🚀 一键部署（推荐）

### 前置要求
- VPS 服务器（Ubuntu 20.04+，2GB+ RAM）
- 域名（如 yourdomain.com）
- SSH 密钥访问权限

### 步骤 1：配置域名解析
在您的域名管理面板添加 A 记录：
```
类型: A
名称: blog
值: your-vps-ip
TTL: 300
```

### 步骤 2：运行自动部署脚本
```bash
# 给脚本执行权限
chmod +x scripts/deploy-to-vps.sh

# 执行部署（替换为您的实际域名和 VPS IP）
./scripts/deploy-to-vps.sh yourdomain.com ******* blog
```

### 步骤 3：访问博客
- 博客首页: https://blog.yourdomain.com
- 管理后台: https://blog.yourdomain.com/ghost/

## 🔧 手动部署

如果自动部署失败，可以按以下步骤手动部署：

### 1. 连接到 VPS
```bash
ssh root@your-vps-ip
```

### 2. 安装 Docker
```bash
# 更新系统
apt update && apt upgrade -y

# 安装 Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装 Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose
```

### 3. 上传项目文件
```bash
# 在本地执行
scp -r ./* root@your-vps-ip:/opt/ghost-blog/
```

### 4. 配置环境变量
```bash
# 在 VPS 上执行
cd /opt/ghost-blog
cp .env.example .env.production

# 编辑配置文件
nano .env.production
```

修改以下配置：
```env
GHOST_URL=https://blog.yourdomain.com
DOMAIN_NAME=blog.yourdomain.com
SSL_EMAIL=<EMAIL>
```

### 5. 获取 SSL 证书
```bash
# 安装 Certbot
apt install certbot -y

# 获取证书
certbot certonly --standalone -d blog.yourdomain.com --email <EMAIL> --agree-tos --non-interactive
```

### 6. 启动服务
```bash
# 创建数据目录
mkdir -p /opt/ghost-blog/data/{ghost-content,mysql-data}
mkdir -p /opt/ghost-blog/logs/nginx

# 启动服务
docker-compose -f docker-compose.prod.yml --env-file .env.production up -d

# 查看状态
docker-compose -f docker-compose.prod.yml ps
```

### 7. 配置防火墙
```bash
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw enable
```

## 🛠️ 常用管理命令

### 查看服务状态
```bash
cd /opt/ghost-blog
docker-compose -f docker-compose.prod.yml ps
```

### 查看日志
```bash
# 查看所有日志
docker-compose -f docker-compose.prod.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.prod.yml logs -f ghost
docker-compose -f docker-compose.prod.yml logs -f nginx
docker-compose -f docker-compose.prod.yml logs -f mysql
```

### 重启服务
```bash
# 重启所有服务
docker-compose -f docker-compose.prod.yml restart

# 重启特定服务
docker-compose -f docker-compose.prod.yml restart ghost
```

### 更新服务
```bash
# 拉取最新镜像
docker-compose -f docker-compose.prod.yml pull

# 重新启动服务
docker-compose -f docker-compose.prod.yml up -d
```

### 备份数据
```bash
# 备份数据库
docker-compose -f docker-compose.prod.yml exec mysql mysqldump -u ghost_user -p ghost_production > backup-$(date +%Y%m%d).sql

# 备份 Ghost 内容
tar -czf ghost-content-backup-$(date +%Y%m%d).tar.gz /opt/ghost-blog/data/ghost-content/
```

## 🔍 故障排除

### 常见问题

#### 1. 域名无法访问
```bash
# 检查 DNS 解析
nslookup blog.yourdomain.com

# 检查防火墙
ufw status

# 检查 Nginx 状态
docker-compose -f docker-compose.prod.yml logs nginx
```

#### 2. SSL 证书问题
```bash
# 检查证书状态
certbot certificates

# 手动续期证书
certbot renew

# 重启 Nginx
docker-compose -f docker-compose.prod.yml restart nginx
```

#### 3. Ghost 无法启动
```bash
# 检查 Ghost 日志
docker-compose -f docker-compose.prod.yml logs ghost

# 检查数据库连接
docker-compose -f docker-compose.prod.yml exec mysql mysql -u ghost_user -p
```

#### 4. 数据库连接失败
```bash
# 检查 MySQL 状态
docker-compose -f docker-compose.prod.yml logs mysql

# 重启 MySQL
docker-compose -f docker-compose.prod.yml restart mysql
```

### 性能优化

#### 1. 启用 Nginx 缓存
编辑 `nginx/conf.d/ssl.conf`，添加缓存配置：
```nginx
# 在 server 块中添加
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=ghost_cache:10m max_size=1g inactive=60m;

# 在 location / 块中添加
proxy_cache ghost_cache;
proxy_cache_valid 200 302 10m;
proxy_cache_valid 404 1m;
```

#### 2. 优化数据库
```bash
# 进入 MySQL 容器
docker-compose -f docker-compose.prod.yml exec mysql mysql -u root -p

# 执行优化命令
OPTIMIZE TABLE posts, users, tags;
```

#### 3. 监控资源使用
```bash
# 查看容器资源使用
docker stats

# 查看磁盘使用
df -h

# 查看内存使用
free -h
```

## 📚 相关文档

- [完整部署指南](docs/vps-deployment-guide.md)
- [中文化配置](docs/chinese-localization.md)
- [用户使用指南](docs/user-guide.md)

## 🎉 部署成功

恭喜！您的 Ghost 博客已成功部署到 VPS。

现在可以：
1. 访问 https://blog.yourdomain.com 查看博客
2. 访问 https://blog.yourdomain.com/ghost/ 进入管理后台
3. 完成初始设置并开始写作

记住定期备份数据和更新系统！

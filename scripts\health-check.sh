#!/bin/bash

# 容器健康检查脚本
# 用于检查各个服务的健康状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查服务是否运行
check_service_running() {
    local service_name=$1
    local container_name=$2
    
    log_info "检查 $service_name 服务状态..."
    
    if docker ps --format "table {{.Names}}" | grep -q "^$container_name$"; then
        log_info "$service_name 容器正在运行"
        return 0
    else
        log_error "$service_name 容器未运行"
        return 1
    fi
}

# 检查 MySQL 健康状态
check_mysql_health() {
    log_info "检查 MySQL 数据库健康状态..."
    
    if ! check_service_running "MySQL" "ghost_mysql"; then
        return 1
    fi
    
    # 检查 MySQL 连接
    if docker exec ghost_mysql mysqladmin ping -h localhost --silent; then
        log_info "MySQL 数据库连接正常"
        
        # 检查数据库是否可以查询
        if docker exec ghost_mysql mysql -u root -p"${MYSQL_ROOT_PASSWORD}" -e "SELECT 1;" > /dev/null 2>&1; then
            log_info "MySQL 数据库查询正常"
            return 0
        else
            log_error "MySQL 数据库查询失败"
            return 1
        fi
    else
        log_error "MySQL 数据库连接失败"
        return 1
    fi
}

# 检查 Ghost 应用健康状态
check_ghost_health() {
    log_info "检查 Ghost 应用健康状态..."
    
    if ! check_service_running "Ghost" "ghost_app"; then
        return 1
    fi
    
    # 检查 Ghost 应用端口
    if docker exec ghost_app netstat -tlnp | grep -q ":2368"; then
        log_info "Ghost 应用端口监听正常"
    else
        log_error "Ghost 应用端口未监听"
        return 1
    fi
    
    # 检查 Ghost 应用 HTTP 响应
    if docker exec ghost_app wget --quiet --tries=1 --spider http://localhost:2368/; then
        log_info "Ghost 应用 HTTP 响应正常"
        return 0
    else
        log_error "Ghost 应用 HTTP 响应失败"
        return 1
    fi
}

# 检查 Nginx 健康状态
check_nginx_health() {
    log_info "检查 Nginx 反向代理健康状态..."
    
    if ! check_service_running "Nginx" "ghost_nginx"; then
        return 1
    fi
    
    # 检查 Nginx 配置
    if docker exec ghost_nginx nginx -t > /dev/null 2>&1; then
        log_info "Nginx 配置文件正确"
    else
        log_error "Nginx 配置文件有错误"
        return 1
    fi
    
    # 检查 Nginx 端口
    if docker exec ghost_nginx netstat -tlnp | grep -q ":80"; then
        log_info "Nginx 端口监听正常"
    else
        log_error "Nginx 端口未监听"
        return 1
    fi
    
    # 检查通过 Nginx 访问 Ghost
    if curl -f http://localhost/ > /dev/null 2>&1; then
        log_info "通过 Nginx 访问 Ghost 正常"
        return 0
    else
        log_error "通过 Nginx 访问 Ghost 失败"
        return 1
    fi
}

# 检查容器资源使用情况
check_resource_usage() {
    log_info "检查容器资源使用情况..."
    
    # 获取容器统计信息
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}" \
        ghost_mysql ghost_app ghost_nginx 2>/dev/null || {
        log_warn "无法获取容器统计信息"
        return 1
    }
}

# 检查磁盘空间
check_disk_space() {
    log_info "检查磁盘空间..."
    
    # 检查 Docker 卷使用情况
    docker system df
    
    # 检查主机磁盘空间
    df -h | grep -E "(/$|/var)"
}

# 主函数
main() {
    log_info "开始系统健康检查..."
    
    local exit_code=0
    
    # 加载环境变量
    if [ -f .env ]; then
        source .env
    else
        log_error ".env 文件不存在"
        exit 1
    fi
    
    # 检查各个服务
    check_mysql_health || exit_code=1
    echo
    
    check_ghost_health || exit_code=1
    echo
    
    check_nginx_health || exit_code=1
    echo
    
    # 检查资源使用情况
    check_resource_usage
    echo
    
    # 检查磁盘空间
    check_disk_space
    echo
    
    if [ $exit_code -eq 0 ]; then
        log_info "所有服务健康检查通过"
    else
        log_error "部分服务健康检查失败"
    fi
    
    return $exit_code
}

# 执行主函数
main "$@"
